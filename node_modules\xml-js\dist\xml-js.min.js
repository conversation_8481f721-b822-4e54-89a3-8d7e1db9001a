!function(t,e){for(var n in e)t[n]=e[n]}(window,function(t){function e(r){if(n[r])return n[r].exports;var i=n[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,e),i.l=!0,i.exports}var n={};return e.m=t,e.c=n,e.d=function(t,n,r){e.o(t,n)||Object.defineProperty(t,n,{configurable:!1,enumerable:!0,get:r})},e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,"a",n),n},e.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},e.p="",e(e.s=21)}([function(t,e,n){"use strict";function r(t){if(!(this instanceof r))return new r(t);c.call(this,t),l.call(this,t),t&&!1===t.readable&&(this.readable=!1),t&&!1===t.writable&&(this.writable=!1),this.allowHalfOpen=!0,t&&!1===t.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",i)}function i(){this.allowHalfOpen||this._writableState.ended||o(a,this)}function a(t){t.end()}var o=n(6),s=Object.keys||function(t){var e=[];for(var n in t)e.push(n);return e};t.exports=r;var u=n(3);u.inherits=n(1);var c=n(16),l=n(10);u.inherits(r,c);for(var f=s(l.prototype),h=0;h<f.length;h++){var d=f[h];r.prototype[d]||(r.prototype[d]=l.prototype[d])}Object.defineProperty(r.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(t){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=t,this._writableState.destroyed=t)}}),r.prototype._destroy=function(t,e){this.push(null),this.end(),o(e,t)}},function(t,e){"function"==typeof Object.create?t.exports=function(t,e){t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}})}:t.exports=function(t,e){t.super_=e;var n=function(){};n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t}},function(t,e){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(t){"object"==typeof window&&(n=window)}t.exports=n},function(t,e,n){(function(t){function n(t){return Array.isArray?Array.isArray(t):"[object Array]"===y(t)}function r(t){return"boolean"==typeof t}function i(t){return null===t}function a(t){return null==t}function o(t){return"number"==typeof t}function s(t){return"string"==typeof t}function u(t){return"symbol"==typeof t}function c(t){return void 0===t}function l(t){return"[object RegExp]"===y(t)}function f(t){return"object"==typeof t&&null!==t}function h(t){return"[object Date]"===y(t)}function d(t){return"[object Error]"===y(t)||t instanceof Error}function p(t){return"function"==typeof t}function g(t){return null===t||"boolean"==typeof t||"number"==typeof t||"string"==typeof t||"symbol"==typeof t||void 0===t}function y(t){return Object.prototype.toString.call(t)}e.isArray=n,e.isBoolean=r,e.isNull=i,e.isNullOrUndefined=a,e.isNumber=o,e.isString=s,e.isSymbol=u,e.isUndefined=c,e.isRegExp=l,e.isObject=f,e.isDate=h,e.isError=d,e.isFunction=p,e.isPrimitive=g,e.isBuffer=t.isBuffer}).call(e,n(4).Buffer)},function(t,e,n){"use strict";(function(t){function r(){return a.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function i(t,e){if(r()<e)throw new RangeError("Invalid typed array length");return a.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(e),t.__proto__=a.prototype):(null===t&&(t=new a(e)),t.length=e),t}function a(t,e,n){if(!(a.TYPED_ARRAY_SUPPORT||this instanceof a))return new a(t,e,n);if("number"==typeof t){if("string"==typeof e)throw new Error("If encoding is specified then the first argument must be a string");return c(this,t)}return o(this,t,e,n)}function o(t,e,n,r){if("number"==typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer?h(t,e,n,r):"string"==typeof e?l(t,e,n):d(t,e)}function s(t){if("number"!=typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function u(t,e,n,r){return s(e),e<=0?i(t,e):void 0!==n?"string"==typeof r?i(t,e).fill(n,r):i(t,e).fill(n):i(t,e)}function c(t,e){if(s(e),t=i(t,e<0?0:0|p(e)),!a.TYPED_ARRAY_SUPPORT)for(var n=0;n<e;++n)t[n]=0;return t}function l(t,e,n){if("string"==typeof n&&""!==n||(n="utf8"),!a.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|y(e,n);t=i(t,r);var o=t.write(e,n);return o!==r&&(t=t.slice(0,o)),t}function f(t,e){var n=e.length<0?0:0|p(e.length);t=i(t,n);for(var r=0;r<n;r+=1)t[r]=255&e[r];return t}function h(t,e,n,r){if(e.byteLength,n<0||e.byteLength<n)throw new RangeError("'offset' is out of bounds");if(e.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");return e=void 0===n&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,n):new Uint8Array(e,n,r),a.TYPED_ARRAY_SUPPORT?(t=e,t.__proto__=a.prototype):t=f(t,e),t}function d(t,e){if(a.isBuffer(e)){var n=0|p(e.length);return t=i(t,n),0===t.length?t:(e.copy(t,0,0,n),t)}if(e){if("undefined"!=typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!=typeof e.length||Q(e.length)?i(t,0):f(t,e);if("Buffer"===e.type&&Z(e.data))return f(t,e.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function p(t){if(t>=r())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+r().toString(16)+" bytes");return 0|t}function g(t){return+t!=t&&(t=0),a.alloc(+t)}function y(t,e){if(a.isBuffer(t))return t.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!=typeof t&&(t=""+t);var n=t.length;if(0===n)return 0;for(var r=!1;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return q(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return X(t).length;default:if(r)return q(t).length;e=(""+e).toLowerCase(),r=!0}}function m(t,e,n){var r=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if(n>>>=0,e>>>=0,n<=e)return"";for(t||(t="utf8");;)switch(t){case"hex":return D(this,e,n);case"utf8":case"utf-8":return I(this,e,n);case"ascii":return F(this,e,n);case"latin1":case"binary":return R(this,e,n);case"base64":return N(this,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return O(this,e,n);default:if(r)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}function b(t,e,n){var r=t[e];t[e]=t[n],t[n]=r}function v(t,e,n,r,i){if(0===t.length)return-1;if("string"==typeof n?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=i?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(i)return-1;n=t.length-1}else if(n<0){if(!i)return-1;n=0}if("string"==typeof e&&(e=a.from(e,r)),a.isBuffer(e))return 0===e.length?-1:E(t,e,n,r,i);if("number"==typeof e)return e&=255,a.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,n):Uint8Array.prototype.lastIndexOf.call(t,e,n):E(t,[e],n,r,i);throw new TypeError("val must be string, number or Buffer")}function E(t,e,n,r,i){function a(t,e){return 1===o?t[e]:t.readUInt16BE(e*o)}var o=1,s=t.length,u=e.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||e.length<2)return-1;o=2,s/=2,u/=2,n/=2}var c;if(i){var l=-1;for(c=n;c<s;c++)if(a(t,c)===a(e,-1===l?0:c-l)){if(-1===l&&(l=c),c-l+1===u)return l*o}else-1!==l&&(c-=c-l),l=-1}else for(n+u>s&&(n=s-u),c=n;c>=0;c--){for(var f=!0,h=0;h<u;h++)if(a(t,c+h)!==a(e,h)){f=!1;break}if(f)return c}return-1}function w(t,e,n,r){n=Number(n)||0;var i=t.length-n;r?(r=Number(r))>i&&(r=i):r=i;var a=e.length;if(a%2!=0)throw new TypeError("Invalid hex string");r>a/2&&(r=a/2);for(var o=0;o<r;++o){var s=parseInt(e.substr(2*o,2),16);if(isNaN(s))return o;t[n+o]=s}return o}function _(t,e,n,r){return H(q(e,t.length-n),t,n,r)}function T(t,e,n,r){return H(G(e),t,n,r)}function A(t,e,n,r){return T(t,e,n,r)}function x(t,e,n,r){return H(X(e),t,n,r)}function S(t,e,n,r){return H(W(e,t.length-n),t,n,r)}function N(t,e,n){return 0===e&&n===t.length?z.fromByteArray(t):z.fromByteArray(t.slice(e,n))}function I(t,e,n){n=Math.min(t.length,n);for(var r=[],i=e;i<n;){var a=t[i],o=null,s=a>239?4:a>223?3:a>191?2:1;if(i+s<=n){var u,c,l,f;switch(s){case 1:a<128&&(o=a);break;case 2:u=t[i+1],128==(192&u)&&(f=(31&a)<<6|63&u)>127&&(o=f);break;case 3:u=t[i+1],c=t[i+2],128==(192&u)&&128==(192&c)&&(f=(15&a)<<12|(63&u)<<6|63&c)>2047&&(f<55296||f>57343)&&(o=f);break;case 4:u=t[i+1],c=t[i+2],l=t[i+3],128==(192&u)&&128==(192&c)&&128==(192&l)&&(f=(15&a)<<18|(63&u)<<12|(63&c)<<6|63&l)>65535&&f<1114112&&(o=f)}}null===o?(o=65533,s=1):o>65535&&(o-=65536,r.push(o>>>10&1023|55296),o=56320|1023&o),r.push(o),i+=s}return C(r)}function C(t){var e=t.length;if(e<=$)return String.fromCharCode.apply(String,t);for(var n="",r=0;r<e;)n+=String.fromCharCode.apply(String,t.slice(r,r+=$));return n}function F(t,e,n){var r="";n=Math.min(t.length,n);for(var i=e;i<n;++i)r+=String.fromCharCode(127&t[i]);return r}function R(t,e,n){var r="";n=Math.min(t.length,n);for(var i=e;i<n;++i)r+=String.fromCharCode(t[i]);return r}function D(t,e,n){var r=t.length;(!e||e<0)&&(e=0),(!n||n<0||n>r)&&(n=r);for(var i="",a=e;a<n;++a)i+=V(t[a]);return i}function O(t,e,n){for(var r=t.slice(e,n),i="",a=0;a<r.length;a+=2)i+=String.fromCharCode(r[a]+256*r[a+1]);return i}function P(t,e,n){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>n)throw new RangeError("Trying to access beyond buffer length")}function L(t,e,n,r,i,o){if(!a.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw new RangeError('"value" argument is out of bounds');if(n+r>t.length)throw new RangeError("Index out of range")}function k(t,e,n,r){e<0&&(e=65535+e+1);for(var i=0,a=Math.min(t.length-n,2);i<a;++i)t[n+i]=(e&255<<8*(r?i:1-i))>>>8*(r?i:1-i)}function B(t,e,n,r){e<0&&(e=4294967295+e+1);for(var i=0,a=Math.min(t.length-n,4);i<a;++i)t[n+i]=e>>>8*(r?i:3-i)&255}function M(t,e,n,r,i,a){if(n+r>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function U(t,e,n,r,i){return i||M(t,e,n,4,3.4028234663852886e38,-3.4028234663852886e38),J.write(t,e,n,r,23,4),n+4}function K(t,e,n,r,i){return i||M(t,e,n,8,1.7976931348623157e308,-1.7976931348623157e308),J.write(t,e,n,r,52,8),n+8}function j(t){if(t=Y(t).replace(tt,""),t.length<2)return"";for(;t.length%4!=0;)t+="=";return t}function Y(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}function V(t){return t<16?"0"+t.toString(16):t.toString(16)}function q(t,e){e=e||1/0;for(var n,r=t.length,i=null,a=[],o=0;o<r;++o){if((n=t.charCodeAt(o))>55295&&n<57344){if(!i){if(n>56319){(e-=3)>-1&&a.push(239,191,189);continue}if(o+1===r){(e-=3)>-1&&a.push(239,191,189);continue}i=n;continue}if(n<56320){(e-=3)>-1&&a.push(239,191,189),i=n;continue}n=65536+(i-55296<<10|n-56320)}else i&&(e-=3)>-1&&a.push(239,191,189);if(i=null,n<128){if((e-=1)<0)break;a.push(n)}else if(n<2048){if((e-=2)<0)break;a.push(n>>6|192,63&n|128)}else if(n<65536){if((e-=3)<0)break;a.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;a.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return a}function G(t){for(var e=[],n=0;n<t.length;++n)e.push(255&t.charCodeAt(n));return e}function W(t,e){for(var n,r,i,a=[],o=0;o<t.length&&!((e-=2)<0);++o)n=t.charCodeAt(o),r=n>>8,i=n%256,a.push(i),a.push(r);return a}function X(t){return z.toByteArray(j(t))}function H(t,e,n,r){for(var i=0;i<r&&!(i+n>=e.length||i>=t.length);++i)e[i+n]=t[i];return i}function Q(t){return t!==t}/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
 * @license  MIT
 */
var z=n(23),J=n(24),Z=n(15);e.Buffer=a,e.SlowBuffer=g,e.INSPECT_MAX_BYTES=50,a.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(t){return!1}}(),e.kMaxLength=r(),a.poolSize=8192,a._augment=function(t){return t.__proto__=a.prototype,t},a.from=function(t,e,n){return o(null,t,e,n)},a.TYPED_ARRAY_SUPPORT&&(a.prototype.__proto__=Uint8Array.prototype,a.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&a[Symbol.species]===a&&Object.defineProperty(a,Symbol.species,{value:null,configurable:!0})),a.alloc=function(t,e,n){return u(null,t,e,n)},a.allocUnsafe=function(t){return c(null,t)},a.allocUnsafeSlow=function(t){return c(null,t)},a.isBuffer=function(t){return!(null==t||!t._isBuffer)},a.compare=function(t,e){if(!a.isBuffer(t)||!a.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var n=t.length,r=e.length,i=0,o=Math.min(n,r);i<o;++i)if(t[i]!==e[i]){n=t[i],r=e[i];break}return n<r?-1:r<n?1:0},a.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(t,e){if(!Z(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return a.alloc(0);var n;if(void 0===e)for(e=0,n=0;n<t.length;++n)e+=t[n].length;var r=a.allocUnsafe(e),i=0;for(n=0;n<t.length;++n){var o=t[n];if(!a.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(r,i),i+=o.length}return r},a.byteLength=y,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)b(this,e,e+1);return this},a.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)b(this,e,e+3),b(this,e+1,e+2);return this},a.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)b(this,e,e+7),b(this,e+1,e+6),b(this,e+2,e+5),b(this,e+3,e+4);return this},a.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?I(this,0,t):m.apply(this,arguments)},a.prototype.equals=function(t){if(!a.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===a.compare(this,t)},a.prototype.inspect=function(){var t="",n=e.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(t+=" ... ")),"<Buffer "+t+">"},a.prototype.compare=function(t,e,n,r,i){if(!a.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===n&&(n=t?t.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),e<0||n>t.length||r<0||i>this.length)throw new RangeError("out of range index");if(r>=i&&e>=n)return 0;if(r>=i)return-1;if(e>=n)return 1;if(e>>>=0,n>>>=0,r>>>=0,i>>>=0,this===t)return 0;for(var o=i-r,s=n-e,u=Math.min(o,s),c=this.slice(r,i),l=t.slice(e,n),f=0;f<u;++f)if(c[f]!==l[f]){o=c[f],s=l[f];break}return o<s?-1:s<o?1:0},a.prototype.includes=function(t,e,n){return-1!==this.indexOf(t,e,n)},a.prototype.indexOf=function(t,e,n){return v(this,t,e,n,!0)},a.prototype.lastIndexOf=function(t,e,n){return v(this,t,e,n,!1)},a.prototype.write=function(t,e,n,r){if(void 0===e)r="utf8",n=this.length,e=0;else if(void 0===n&&"string"==typeof e)r=e,n=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var i=this.length-e;if((void 0===n||n>i)&&(n=i),t.length>0&&(n<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var a=!1;;)switch(r){case"hex":return w(this,t,e,n);case"utf8":case"utf-8":return _(this,t,e,n);case"ascii":return T(this,t,e,n);case"latin1":case"binary":return A(this,t,e,n);case"base64":return x(this,t,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return S(this,t,e,n);default:if(a)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),a=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var $=4096;a.prototype.slice=function(t,e){var n=this.length;t=~~t,e=void 0===e?n:~~e,t<0?(t+=n)<0&&(t=0):t>n&&(t=n),e<0?(e+=n)<0&&(e=0):e>n&&(e=n),e<t&&(e=t);var r;if(a.TYPED_ARRAY_SUPPORT)r=this.subarray(t,e),r.__proto__=a.prototype;else{var i=e-t;r=new a(i,void 0);for(var o=0;o<i;++o)r[o]=this[o+t]}return r},a.prototype.readUIntLE=function(t,e,n){t|=0,e|=0,n||P(t,e,this.length);for(var r=this[t],i=1,a=0;++a<e&&(i*=256);)r+=this[t+a]*i;return r},a.prototype.readUIntBE=function(t,e,n){t|=0,e|=0,n||P(t,e,this.length);for(var r=this[t+--e],i=1;e>0&&(i*=256);)r+=this[t+--e]*i;return r},a.prototype.readUInt8=function(t,e){return e||P(t,1,this.length),this[t]},a.prototype.readUInt16LE=function(t,e){return e||P(t,2,this.length),this[t]|this[t+1]<<8},a.prototype.readUInt16BE=function(t,e){return e||P(t,2,this.length),this[t]<<8|this[t+1]},a.prototype.readUInt32LE=function(t,e){return e||P(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},a.prototype.readUInt32BE=function(t,e){return e||P(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},a.prototype.readIntLE=function(t,e,n){t|=0,e|=0,n||P(t,e,this.length);for(var r=this[t],i=1,a=0;++a<e&&(i*=256);)r+=this[t+a]*i;return i*=128,r>=i&&(r-=Math.pow(2,8*e)),r},a.prototype.readIntBE=function(t,e,n){t|=0,e|=0,n||P(t,e,this.length);for(var r=e,i=1,a=this[t+--r];r>0&&(i*=256);)a+=this[t+--r]*i;return i*=128,a>=i&&(a-=Math.pow(2,8*e)),a},a.prototype.readInt8=function(t,e){return e||P(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},a.prototype.readInt16LE=function(t,e){e||P(t,2,this.length);var n=this[t]|this[t+1]<<8;return 32768&n?4294901760|n:n},a.prototype.readInt16BE=function(t,e){e||P(t,2,this.length);var n=this[t+1]|this[t]<<8;return 32768&n?4294901760|n:n},a.prototype.readInt32LE=function(t,e){return e||P(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},a.prototype.readInt32BE=function(t,e){return e||P(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},a.prototype.readFloatLE=function(t,e){return e||P(t,4,this.length),J.read(this,t,!0,23,4)},a.prototype.readFloatBE=function(t,e){return e||P(t,4,this.length),J.read(this,t,!1,23,4)},a.prototype.readDoubleLE=function(t,e){return e||P(t,8,this.length),J.read(this,t,!0,52,8)},a.prototype.readDoubleBE=function(t,e){return e||P(t,8,this.length),J.read(this,t,!1,52,8)},a.prototype.writeUIntLE=function(t,e,n,r){if(t=+t,e|=0,n|=0,!r){L(this,t,e,n,Math.pow(2,8*n)-1,0)}var i=1,a=0;for(this[e]=255&t;++a<n&&(i*=256);)this[e+a]=t/i&255;return e+n},a.prototype.writeUIntBE=function(t,e,n,r){if(t=+t,e|=0,n|=0,!r){L(this,t,e,n,Math.pow(2,8*n)-1,0)}var i=n-1,a=1;for(this[e+i]=255&t;--i>=0&&(a*=256);)this[e+i]=t/a&255;return e+n},a.prototype.writeUInt8=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,1,255,0),a.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=255&t,e+1},a.prototype.writeUInt16LE=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,2,65535,0),a.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):k(this,t,e,!0),e+2},a.prototype.writeUInt16BE=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,2,65535,0),a.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):k(this,t,e,!1),e+2},a.prototype.writeUInt32LE=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,4,4294967295,0),a.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t):B(this,t,e,!0),e+4},a.prototype.writeUInt32BE=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,4,4294967295,0),a.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):B(this,t,e,!1),e+4},a.prototype.writeIntLE=function(t,e,n,r){if(t=+t,e|=0,!r){var i=Math.pow(2,8*n-1);L(this,t,e,n,i-1,-i)}var a=0,o=1,s=0;for(this[e]=255&t;++a<n&&(o*=256);)t<0&&0===s&&0!==this[e+a-1]&&(s=1),this[e+a]=(t/o>>0)-s&255;return e+n},a.prototype.writeIntBE=function(t,e,n,r){if(t=+t,e|=0,!r){var i=Math.pow(2,8*n-1);L(this,t,e,n,i-1,-i)}var a=n-1,o=1,s=0;for(this[e+a]=255&t;--a>=0&&(o*=256);)t<0&&0===s&&0!==this[e+a+1]&&(s=1),this[e+a]=(t/o>>0)-s&255;return e+n},a.prototype.writeInt8=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,1,127,-128),a.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[e]=255&t,e+1},a.prototype.writeInt16LE=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,2,32767,-32768),a.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):k(this,t,e,!0),e+2},a.prototype.writeInt16BE=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,2,32767,-32768),a.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):k(this,t,e,!1),e+2},a.prototype.writeInt32LE=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,4,2147483647,-2147483648),a.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):B(this,t,e,!0),e+4},a.prototype.writeInt32BE=function(t,e,n){return t=+t,e|=0,n||L(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),a.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):B(this,t,e,!1),e+4},a.prototype.writeFloatLE=function(t,e,n){return U(this,t,e,!0,n)},a.prototype.writeFloatBE=function(t,e,n){return U(this,t,e,!1,n)},a.prototype.writeDoubleLE=function(t,e,n){return K(this,t,e,!0,n)},a.prototype.writeDoubleBE=function(t,e,n){return K(this,t,e,!1,n)},a.prototype.copy=function(t,e,n,r){if(n||(n=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);var i,o=r-n;if(this===t&&n<e&&e<r)for(i=o-1;i>=0;--i)t[i+e]=this[i+n];else if(o<1e3||!a.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)t[i+e]=this[i+n];else Uint8Array.prototype.set.call(t,this.subarray(n,n+o),e);return o},a.prototype.fill=function(t,e,n,r){if("string"==typeof t){if("string"==typeof e?(r=e,e=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===t.length){var i=t.charCodeAt(0);i<256&&(t=i)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!a.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof t&&(t&=255);if(e<0||this.length<e||this.length<n)throw new RangeError("Out of range index");if(n<=e)return this;e>>>=0,n=void 0===n?this.length:n>>>0,t||(t=0);var o;if("number"==typeof t)for(o=e;o<n;++o)this[o]=t;else{var s=a.isBuffer(t)?t:q(new a(t,r).toString()),u=s.length;for(o=0;o<n-e;++o)this[o+e]=s[o%u]}return this};var tt=/[^+\/0-9A-Za-z-_]/g}).call(e,n(2))},function(t,e){function n(){throw new Error("setTimeout has not been defined")}function r(){throw new Error("clearTimeout has not been defined")}function i(t){if(l===setTimeout)return setTimeout(t,0);if((l===n||!l)&&setTimeout)return l=setTimeout,setTimeout(t,0);try{return l(t,0)}catch(e){try{return l.call(null,t,0)}catch(e){return l.call(this,t,0)}}}function a(t){if(f===clearTimeout)return clearTimeout(t);if((f===r||!f)&&clearTimeout)return f=clearTimeout,clearTimeout(t);try{return f(t)}catch(e){try{return f.call(null,t)}catch(e){return f.call(this,t)}}}function o(){g&&d&&(g=!1,d.length?p=d.concat(p):y=-1,p.length&&s())}function s(){if(!g){var t=i(o);g=!0;for(var e=p.length;e;){for(d=p,p=[];++y<e;)d&&d[y].run();y=-1,e=p.length}d=null,g=!1,a(t)}}function u(t,e){this.fun=t,this.array=e}function c(){}var l,f,h=t.exports={};!function(){try{l="function"==typeof setTimeout?setTimeout:n}catch(t){l=n}try{f="function"==typeof clearTimeout?clearTimeout:r}catch(t){f=r}}();var d,p=[],g=!1,y=-1;h.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];p.push(new u(t,e)),1!==p.length||g||i(s)},u.prototype.run=function(){this.fun.apply(null,this.array)},h.title="browser",h.browser=!0,h.env={},h.argv=[],h.version="",h.versions={},h.on=c,h.addListener=c,h.once=c,h.off=c,h.removeListener=c,h.removeAllListeners=c,h.emit=c,h.prependListener=c,h.prependOnceListener=c,h.listeners=function(t){return[]},h.binding=function(t){throw new Error("process.binding is not supported")},h.cwd=function(){return"/"},h.chdir=function(t){throw new Error("process.chdir is not supported")},h.umask=function(){return 0}},function(t,e,n){"use strict";(function(e){function n(t,n,r,i){if("function"!=typeof t)throw new TypeError('"callback" argument must be a function');var a,o,s=arguments.length;switch(s){case 0:case 1:return e.nextTick(t);case 2:return e.nextTick(function(){t.call(null,n)});case 3:return e.nextTick(function(){t.call(null,n,r)});case 4:return e.nextTick(function(){t.call(null,n,r,i)});default:for(a=new Array(s-1),o=0;o<a.length;)a[o++]=arguments[o];return e.nextTick(function(){t.apply(null,a)})}}!e.version||0===e.version.indexOf("v0.")||0===e.version.indexOf("v1.")&&0!==e.version.indexOf("v1.8.")?t.exports=n:t.exports=e.nextTick}).call(e,n(5))},function(t,e,n){function r(t,e){for(var n in t)e[n]=t[n]}function i(t,e,n){return o(t,e,n)}var a=n(4),o=a.Buffer;o.from&&o.alloc&&o.allocUnsafe&&o.allocUnsafeSlow?t.exports=a:(r(a,e),e.Buffer=i),r(o,i),i.from=function(t,e,n){if("number"==typeof t)throw new TypeError("Argument must not be a number");return o(t,e,n)},i.alloc=function(t,e,n){if("number"!=typeof t)throw new TypeError("Argument must be a number");var r=o(t);return void 0!==e?"string"==typeof n?r.fill(e,n):r.fill(e):r.fill(0),r},i.allocUnsafe=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return o(t)},i.allocUnsafeSlow=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return a.SlowBuffer(t)}},function(t,e){function n(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}function r(t){return"function"==typeof t}function i(t){return"number"==typeof t}function a(t){return"object"==typeof t&&null!==t}function o(t){return void 0===t}t.exports=n,n.EventEmitter=n,n.prototype._events=void 0,n.prototype._maxListeners=void 0,n.defaultMaxListeners=10,n.prototype.setMaxListeners=function(t){if(!i(t)||t<0||isNaN(t))throw TypeError("n must be a positive number");return this._maxListeners=t,this},n.prototype.emit=function(t){var e,n,i,s,u,c;if(this._events||(this._events={}),"error"===t&&(!this._events.error||a(this._events.error)&&!this._events.error.length)){if((e=arguments[1])instanceof Error)throw e;var l=new Error('Uncaught, unspecified "error" event. ('+e+")");throw l.context=e,l}if(n=this._events[t],o(n))return!1;if(r(n))switch(arguments.length){case 1:n.call(this);break;case 2:n.call(this,arguments[1]);break;case 3:n.call(this,arguments[1],arguments[2]);break;default:s=Array.prototype.slice.call(arguments,1),n.apply(this,s)}else if(a(n))for(s=Array.prototype.slice.call(arguments,1),c=n.slice(),i=c.length,u=0;u<i;u++)c[u].apply(this,s);return!0},n.prototype.addListener=function(t,e){var i;if(!r(e))throw TypeError("listener must be a function");return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",t,r(e.listener)?e.listener:e),this._events[t]?a(this._events[t])?this._events[t].push(e):this._events[t]=[this._events[t],e]:this._events[t]=e,a(this._events[t])&&!this._events[t].warned&&(i=o(this._maxListeners)?n.defaultMaxListeners:this._maxListeners)&&i>0&&this._events[t].length>i&&(this._events[t].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[t].length),"function"==typeof console.trace&&console.trace()),this},n.prototype.on=n.prototype.addListener,n.prototype.once=function(t,e){function n(){this.removeListener(t,n),i||(i=!0,e.apply(this,arguments))}if(!r(e))throw TypeError("listener must be a function");var i=!1;return n.listener=e,this.on(t,n),this},n.prototype.removeListener=function(t,e){var n,i,o,s;if(!r(e))throw TypeError("listener must be a function");if(!this._events||!this._events[t])return this;if(n=this._events[t],o=n.length,i=-1,n===e||r(n.listener)&&n.listener===e)delete this._events[t],this._events.removeListener&&this.emit("removeListener",t,e);else if(a(n)){for(s=o;s-- >0;)if(n[s]===e||n[s].listener&&n[s].listener===e){i=s;break}if(i<0)return this;1===n.length?(n.length=0,delete this._events[t]):n.splice(i,1),this._events.removeListener&&this.emit("removeListener",t,e)}return this},n.prototype.removeAllListeners=function(t){var e,n;if(!this._events)return this;if(!this._events.removeListener)return 0===arguments.length?this._events={}:this._events[t]&&delete this._events[t],this;if(0===arguments.length){for(e in this._events)"removeListener"!==e&&this.removeAllListeners(e);return this.removeAllListeners("removeListener"),this._events={},this}if(n=this._events[t],r(n))this.removeListener(t,n);else if(n)for(;n.length;)this.removeListener(t,n[n.length-1]);return delete this._events[t],this},n.prototype.listeners=function(t){return this._events&&this._events[t]?r(this._events[t])?[this._events[t]]:this._events[t].slice():[]},n.prototype.listenerCount=function(t){if(this._events){var e=this._events[t];if(r(e))return 1;if(e)return e.length}return 0},n.listenerCount=function(t,e){return t.listenerCount(e)}},function(t,e,n){e=t.exports=n(16),e.Stream=e,e.Readable=e,e.Writable=n(10),e.Duplex=n(0),e.Transform=n(19),e.PassThrough=n(31)},function(t,e,n){"use strict";(function(e,r,i){function a(t){var e=this;this.next=null,this.entry=null,this.finish=function(){N(e,t)}}function o(t){return P.from(t)}function s(t){return P.isBuffer(t)||t instanceof L}function u(){}function c(t,e){C=C||n(0),t=t||{},this.objectMode=!!t.objectMode,e instanceof C&&(this.objectMode=this.objectMode||!!t.writableObjectMode);var r=t.highWaterMark,i=this.objectMode?16:16384;this.highWaterMark=r||0===r?r:i,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var o=!1===t.decodeStrings;this.decodeStrings=!o,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(t){b(e,t)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new a(this)}function l(t){if(C=C||n(0),!(B.call(l,this)||this instanceof C))return new l(t);this._writableState=new c(t,this),this.writable=!0,t&&("function"==typeof t.write&&(this._write=t.write),"function"==typeof t.writev&&(this._writev=t.writev),"function"==typeof t.destroy&&(this._destroy=t.destroy),"function"==typeof t.final&&(this._final=t.final)),O.call(this)}function f(t,e){var n=new Error("write after end");t.emit("error",n),I(e,n)}function h(t,e,n,r){var i=!0,a=!1;return null===n?a=new TypeError("May not write null values to stream"):"string"==typeof n||void 0===n||e.objectMode||(a=new TypeError("Invalid non-string/buffer chunk")),a&&(t.emit("error",a),I(r,a),i=!1),i}function d(t,e,n){return t.objectMode||!1===t.decodeStrings||"string"!=typeof e||(e=P.from(e,n)),e}function p(t,e,n,r,i,a){if(!n){var o=d(e,r,i);r!==o&&(n=!0,i="buffer",r=o)}var s=e.objectMode?1:r.length;e.length+=s;var u=e.length<e.highWaterMark;if(u||(e.needDrain=!0),e.writing||e.corked){var c=e.lastBufferedRequest;e.lastBufferedRequest={chunk:r,encoding:i,isBuf:n,callback:a,next:null},c?c.next=e.lastBufferedRequest:e.bufferedRequest=e.lastBufferedRequest,e.bufferedRequestCount+=1}else g(t,e,!1,s,r,i,a);return u}function g(t,e,n,r,i,a,o){e.writelen=r,e.writecb=o,e.writing=!0,e.sync=!0,n?t._writev(i,e.onwrite):t._write(i,a,e.onwrite),e.sync=!1}function y(t,e,n,r,i){--e.pendingcb,n?(I(i,r),I(x,t,e),t._writableState.errorEmitted=!0,t.emit("error",r)):(i(r),t._writableState.errorEmitted=!0,t.emit("error",r),x(t,e))}function m(t){t.writing=!1,t.writecb=null,t.length-=t.writelen,t.writelen=0}function b(t,e){var n=t._writableState,r=n.sync,i=n.writecb;if(m(n),e)y(t,n,r,e,i);else{var a=_(n);a||n.corked||n.bufferProcessing||!n.bufferedRequest||w(t,n),r?F(v,t,n,a,i):v(t,n,a,i)}}function v(t,e,n,r){n||E(t,e),e.pendingcb--,r(),x(t,e)}function E(t,e){0===e.length&&e.needDrain&&(e.needDrain=!1,t.emit("drain"))}function w(t,e){e.bufferProcessing=!0;var n=e.bufferedRequest;if(t._writev&&n&&n.next){var r=e.bufferedRequestCount,i=new Array(r),o=e.corkedRequestsFree;o.entry=n;for(var s=0,u=!0;n;)i[s]=n,n.isBuf||(u=!1),n=n.next,s+=1;i.allBuffers=u,g(t,e,!0,e.length,i,"",o.finish),e.pendingcb++,e.lastBufferedRequest=null,o.next?(e.corkedRequestsFree=o.next,o.next=null):e.corkedRequestsFree=new a(e)}else{for(;n;){var c=n.chunk,l=n.encoding,f=n.callback;if(g(t,e,!1,e.objectMode?1:c.length,c,l,f),n=n.next,e.writing)break}null===n&&(e.lastBufferedRequest=null)}e.bufferedRequestCount=0,e.bufferedRequest=n,e.bufferProcessing=!1}function _(t){return t.ending&&0===t.length&&null===t.bufferedRequest&&!t.finished&&!t.writing}function T(t,e){t._final(function(n){e.pendingcb--,n&&t.emit("error",n),e.prefinished=!0,t.emit("prefinish"),x(t,e)})}function A(t,e){e.prefinished||e.finalCalled||("function"==typeof t._final?(e.pendingcb++,e.finalCalled=!0,I(T,t,e)):(e.prefinished=!0,t.emit("prefinish")))}function x(t,e){var n=_(e);return n&&(A(t,e),0===e.pendingcb&&(e.finished=!0,t.emit("finish"))),n}function S(t,e,n){e.ending=!0,x(t,e),n&&(e.finished?I(n):t.once("finish",n)),e.ended=!0,t.writable=!1}function N(t,e,n){var r=t.entry;for(t.entry=null;r;){var i=r.callback;e.pendingcb--,i(n),r=r.next}e.corkedRequestsFree?e.corkedRequestsFree.next=t:e.corkedRequestsFree=t}var I=n(6);t.exports=l;var C,F=!e.browser&&["v0.10","v0.9."].indexOf(e.version.slice(0,5))>-1?r:I;l.WritableState=c;var R=n(3);R.inherits=n(1);var D={deprecate:n(30)},O=n(17),P=n(7).Buffer,L=i.Uint8Array||function(){},k=n(18);R.inherits(l,O),c.prototype.getBuffer=function(){for(var t=this.bufferedRequest,e=[];t;)e.push(t),t=t.next;return e},function(){try{Object.defineProperty(c.prototype,"buffer",{get:D.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(t){}}();var B;"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(B=Function.prototype[Symbol.hasInstance],Object.defineProperty(l,Symbol.hasInstance,{value:function(t){return!!B.call(this,t)||t&&t._writableState instanceof c}})):B=function(t){return t instanceof this},l.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},l.prototype.write=function(t,e,n){var r=this._writableState,i=!1,a=s(t)&&!r.objectMode;return a&&!P.isBuffer(t)&&(t=o(t)),"function"==typeof e&&(n=e,e=null),a?e="buffer":e||(e=r.defaultEncoding),"function"!=typeof n&&(n=u),r.ended?f(this,n):(a||h(this,r,t,n))&&(r.pendingcb++,i=p(this,r,a,t,e,n)),i},l.prototype.cork=function(){this._writableState.corked++},l.prototype.uncork=function(){var t=this._writableState;t.corked&&(t.corked--,t.writing||t.corked||t.finished||t.bufferProcessing||!t.bufferedRequest||w(this,t))},l.prototype.setDefaultEncoding=function(t){if("string"==typeof t&&(t=t.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((t+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+t);return this._writableState.defaultEncoding=t,this},l.prototype._write=function(t,e,n){n(new Error("_write() is not implemented"))},l.prototype._writev=null,l.prototype.end=function(t,e,n){var r=this._writableState;"function"==typeof t?(n=t,t=null,e=null):"function"==typeof e&&(n=e,e=null),null!==t&&void 0!==t&&this.write(t,e),r.corked&&(r.corked=1,this.uncork()),r.ending||r.finished||S(this,r,n)},Object.defineProperty(l.prototype,"destroyed",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(t){this._writableState&&(this._writableState.destroyed=t)}}),l.prototype.destroy=k.destroy,l.prototype._undestroy=k.undestroy,l.prototype._destroy=function(t,e){this.end(),e(t)}}).call(e,n(5),n(28).setImmediate,n(2))},function(t,e,n){"use strict";function r(t){if(!t)return"utf8";for(var e;;)switch(t){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return t;default:if(e)return;t=(""+t).toLowerCase(),e=!0}}function i(t){var e=r(t);if("string"!=typeof e&&(b.isEncoding===v||!v(t)))throw new Error("Unknown encoding: "+t);return e||t}function a(t){this.encoding=i(t);var e;switch(this.encoding){case"utf16le":this.text=h,this.end=d,e=4;break;case"utf8":this.fillLast=c,e=4;break;case"base64":this.text=p,this.end=g,e=3;break;default:return this.write=y,void(this.end=m)}this.lastNeed=0,this.lastTotal=0,this.lastChar=b.allocUnsafe(e)}function o(t){return t<=127?0:t>>5==6?2:t>>4==14?3:t>>3==30?4:-1}function s(t,e,n){var r=e.length-1;if(r<n)return 0;var i=o(e[r]);return i>=0?(i>0&&(t.lastNeed=i-1),i):--r<n?0:(i=o(e[r]))>=0?(i>0&&(t.lastNeed=i-2),i):--r<n?0:(i=o(e[r]),i>=0?(i>0&&(2===i?i=0:t.lastNeed=i-3),i):0)}function u(t,e,n){if(128!=(192&e[0]))return t.lastNeed=0,"�".repeat(n);if(t.lastNeed>1&&e.length>1){if(128!=(192&e[1]))return t.lastNeed=1,"�".repeat(n+1);if(t.lastNeed>2&&e.length>2&&128!=(192&e[2]))return t.lastNeed=2,"�".repeat(n+2)}}function c(t){var e=this.lastTotal-this.lastNeed,n=u(this,t,e);return void 0!==n?n:this.lastNeed<=t.length?(t.copy(this.lastChar,e,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(t.copy(this.lastChar,e,0,t.length),void(this.lastNeed-=t.length))}function l(t,e){var n=s(this,t,e);if(!this.lastNeed)return t.toString("utf8",e);this.lastTotal=n;var r=t.length-(n-this.lastNeed);return t.copy(this.lastChar,0,r),t.toString("utf8",e,r)}function f(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+"�".repeat(this.lastTotal-this.lastNeed):e}function h(t,e){if((t.length-e)%2==0){var n=t.toString("utf16le",e);if(n){var r=n.charCodeAt(n.length-1);if(r>=55296&&r<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1],n.slice(0,-1)}return n}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=t[t.length-1],t.toString("utf16le",e,t.length-1)}function d(t){var e=t&&t.length?this.write(t):"";if(this.lastNeed){var n=this.lastTotal-this.lastNeed;return e+this.lastChar.toString("utf16le",0,n)}return e}function p(t,e){var n=(t.length-e)%3;return 0===n?t.toString("base64",e):(this.lastNeed=3-n,this.lastTotal=3,1===n?this.lastChar[0]=t[t.length-1]:(this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1]),t.toString("base64",e,t.length-n))}function g(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+this.lastChar.toString("base64",0,3-this.lastNeed):e}function y(t){return t.toString(this.encoding)}function m(t){return t&&t.length?this.write(t):""}var b=n(7).Buffer,v=b.isEncoding||function(t){switch((t=""+t)&&t.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};e.StringDecoder=a,a.prototype.write=function(t){if(0===t.length)return"";var e,n;if(this.lastNeed){if(void 0===(e=this.fillLast(t)))return"";n=this.lastNeed,this.lastNeed=0}else n=0;return n<t.length?e?e+this.text(t,n):this.text(t,n):e||""},a.prototype.end=f,a.prototype.text=l,a.prototype.fillLast=function(t){if(this.lastNeed<=t.length)return t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,t.length),this.lastNeed-=t.length}},function(t,e,n){var r=n(13).isArray;t.exports={copyOptions:function(t){var e,n={};for(e in t)t.hasOwnProperty(e)&&(n[e]=t[e]);return n},ensureFlagExists:function(t,e){t in e&&"boolean"==typeof e[t]||(e[t]=!1)},ensureSpacesExists:function(t){"spaces"in t&&("number"==typeof t.spaces||"string"==typeof t.spaces)||(t.spaces=0)},ensureAlwaysArrayExists:function(t){"alwaysArray"in t&&("boolean"==typeof t.alwaysArray||r(t.alwaysArray))||(t.alwaysArray=!1)},ensureKeyExists:function(t,e){t+"Key"in e&&"string"==typeof e[t+"Key"]||(e[t+"Key"]=e.compact?"_"+t:t)},checkFnExists:function(t,e){return t+"Fn"in e}}},function(t,e){t.exports={isArray:function(t){return Array.isArray?Array.isArray(t):"[object Array]"===Object.prototype.toString.call(t)}}},function(t,e,n){function r(t){return g=b.copyOptions(t),b.ensureFlagExists("ignoreDeclaration",g),b.ensureFlagExists("ignoreInstruction",g),b.ensureFlagExists("ignoreAttributes",g),b.ensureFlagExists("ignoreText",g),b.ensureFlagExists("ignoreComment",g),b.ensureFlagExists("ignoreCdata",g),b.ensureFlagExists("ignoreDoctype",g),b.ensureFlagExists("compact",g),b.ensureFlagExists("alwaysChildren",g),b.ensureFlagExists("addParent",g),b.ensureFlagExists("trim",g),b.ensureFlagExists("nativeType",g),b.ensureFlagExists("sanitize",g),b.ensureFlagExists("instructionHasAttributes",g),b.ensureFlagExists("captureSpacesBetweenElements",g),b.ensureAlwaysArrayExists(g),b.ensureKeyExists("declaration",g),b.ensureKeyExists("instruction",g),b.ensureKeyExists("attributes",g),b.ensureKeyExists("text",g),b.ensureKeyExists("comment",g),b.ensureKeyExists("cdata",g),b.ensureKeyExists("doctype",g),b.ensureKeyExists("type",g),b.ensureKeyExists("name",g),b.ensureKeyExists("elements",g),b.ensureKeyExists("parent",g),b.checkFnExists("doctype",g),b.checkFnExists("instruction",g),b.checkFnExists("cdata",g),b.checkFnExists("comment",g),b.checkFnExists("text",g),b.checkFnExists("instructionName",g),b.checkFnExists("elementName",g),b.checkFnExists("attributeName",g),b.checkFnExists("attributeValue",g),b.checkFnExists("attributes",g),g}function i(t){var e=Number(t);if(!isNaN(e))return e;var n=t.toLowerCase();return"true"===n||"false"!==n&&t}function a(t,e){var n;if(g.compact){if(!y[g[t+"Key"]]&&(v(g.alwaysArray)?-1!==g.alwaysArray.indexOf(g[t+"Key"]):g.alwaysArray)&&(y[g[t+"Key"]]=[]),y[g[t+"Key"]]&&!v(y[g[t+"Key"]])&&(y[g[t+"Key"]]=[y[g[t+"Key"]]]),t+"Fn"in g&&"string"==typeof e&&(e=g[t+"Fn"](e,y)),"instruction"===t&&("instructionFn"in g||"instructionNameFn"in g))for(n in e)if(e.hasOwnProperty(n))if("instructionFn"in g)e[n]=g.instructionFn(e[n],n,y);else{var r=e[n];delete e[n],e[g.instructionNameFn(n,r,y)]=r}v(y[g[t+"Key"]])?y[g[t+"Key"]].push(e):y[g[t+"Key"]]=e}else{y[g.elementsKey]||(y[g.elementsKey]=[]);var i={};if(i[g.typeKey]=t,"instruction"===t){for(n in e)if(e.hasOwnProperty(n))break;i[g.nameKey]="instructionNameFn"in g?g.instructionNameFn(n,e,y):n,g.instructionHasAttributes?(i[g.attributesKey]=e[n][g.attributesKey],"instructionFn"in g&&(i[g.attributesKey]=g.instructionFn(i[g.attributesKey],n,y))):("instructionFn"in g&&(e[n]=g.instructionFn(e[n],n,y)),i[g.instructionKey]=e[n])}else t+"Fn"in g&&(e=g[t+"Fn"](e,y)),i[g[t+"Key"]]=e;g.addParent&&(i[g.parentKey]=y),y[g.elementsKey].push(i)}}function o(t){if("attributesFn"in g&&t&&(t=g.attributesFn(t,y)),(g.trim||"attributeValueFn"in g||"attributeNameFn"in g)&&t){var e;for(e in t)if(t.hasOwnProperty(e)&&(g.trim&&(t[e]=t[e].trim()),"attributeValueFn"in g&&(t[e]=g.attributeValueFn(t[e],e,y)),"attributeNameFn"in g)){var n=t[e];delete t[e],t[g.attributeNameFn(e,t[e],y)]=n}}return t}function s(t){var e={};if(t.body&&("xml"===t.name.toLowerCase()||g.instructionHasAttributes)){for(var n,r=/([\w:-]+)\s*=\s*(?:"([^"]*)"|'([^']*)'|(\w+))\s*/g;null!==(n=r.exec(t.body));)e[n[1]]=n[2]||n[3]||n[4];e=o(e)}if("xml"===t.name.toLowerCase()){if(g.ignoreDeclaration)return;y[g.declarationKey]={},Object.keys(e).length&&(y[g.declarationKey][g.attributesKey]=e),g.addParent&&(y[g.declarationKey][g.parentKey]=y)}else{if(g.ignoreInstruction)return;g.trim&&(t.body=t.body.trim());var i={};g.instructionHasAttributes&&Object.keys(e).length?(i[t.name]={},i[t.name][g.attributesKey]=e):i[t.name]=t.body,a("instruction",i)}}function u(t,e){var n;if("object"==typeof t&&(e=t.attributes,t=t.name),e=o(e),"elementNameFn"in g&&(t=g.elementNameFn(t,y)),g.compact){if(n={},!g.ignoreAttributes&&e&&Object.keys(e).length){n[g.attributesKey]={};var r;for(r in e)e.hasOwnProperty(r)&&(n[g.attributesKey][r]=e[r])}t in y||(v(g.alwaysArray)?-1===g.alwaysArray.indexOf(t):!g.alwaysArray)||(y[t]=[]),y[t]&&!v(y[t])&&(y[t]=[y[t]]),v(y[t])?y[t].push(n):y[t]=n}else y[g.elementsKey]||(y[g.elementsKey]=[]),n={},n[g.typeKey]="element",n[g.nameKey]=t,!g.ignoreAttributes&&e&&Object.keys(e).length&&(n[g.attributesKey]=e),g.alwaysChildren&&(n[g.elementsKey]=[]),y[g.elementsKey].push(n);n[g.parentKey]=y,y=n}function c(t){g.ignoreText||(t.trim()||g.captureSpacesBetweenElements)&&(g.trim&&(t=t.trim()),g.nativeType&&(t=i(t)),g.sanitize&&(t=t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")),a("text",t))}function l(t){g.ignoreComment||(g.trim&&(t=t.trim()),a("comment",t))}function f(t){var e=y[g.parentKey];g.addParent||delete y[g.parentKey],y=e}function h(t){g.ignoreCdata||(g.trim&&(t=t.trim()),a("cdata",t))}function d(t){g.ignoreDoctype||(t=t.replace(/^ /,""),g.trim&&(t=t.trim()),a("doctype",t))}function p(t){t.note=t}var g,y,m=n(22),b=n(12),v=n(13).isArray;t.exports=function(t,e){var n=m.parser(!0,{}),i={};if(y=i,g=r(e),n.opt={strictEntities:!0},n.onopentag=u,n.ontext=c,n.oncomment=l,n.onclosetag=f,n.onerror=p,n.oncdata=h,n.ondoctype=d,n.onprocessinginstruction=s,n.write(t).close(),i[g.elementsKey]){var a=i[g.elementsKey];delete i[g.elementsKey],i[g.elementsKey]=a,delete i.text}return i}},function(t,e){var n={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==n.call(t)}},function(t,e,n){"use strict";(function(e,r){function i(t){return B.from(t)}function a(t){return B.isBuffer(t)||t instanceof M}function o(t,e,n){if("function"==typeof t.prependListener)return t.prependListener(e,n);t._events&&t._events[e]?P(t._events[e])?t._events[e].unshift(n):t._events[e]=[n,t._events[e]]:t.on(e,n)}function s(t,e){O=O||n(0),t=t||{},this.objectMode=!!t.objectMode,e instanceof O&&(this.objectMode=this.objectMode||!!t.readableObjectMode);var r=t.highWaterMark,i=this.objectMode?16:16384;this.highWaterMark=r||0===r?r:i,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new V,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(Y||(Y=n(11).StringDecoder),this.decoder=new Y(t.encoding),this.encoding=t.encoding)}function u(t){if(O=O||n(0),!(this instanceof u))return new u(t);this._readableState=new s(t,this),this.readable=!0,t&&("function"==typeof t.read&&(this._read=t.read),"function"==typeof t.destroy&&(this._destroy=t.destroy)),k.call(this)}function c(t,e,n,r,a){var o=t._readableState;if(null===e)o.reading=!1,g(t,o);else{var s;a||(s=f(o,e)),s?t.emit("error",s):o.objectMode||e&&e.length>0?("string"==typeof e||o.objectMode||Object.getPrototypeOf(e)===B.prototype||(e=i(e)),r?o.endEmitted?t.emit("error",new Error("stream.unshift() after end event")):l(t,o,e,!0):o.ended?t.emit("error",new Error("stream.push() after EOF")):(o.reading=!1,o.decoder&&!n?(e=o.decoder.write(e),o.objectMode||0!==e.length?l(t,o,e,!1):b(t,o)):l(t,o,e,!1))):r||(o.reading=!1)}return h(o)}function l(t,e,n,r){e.flowing&&0===e.length&&!e.sync?(t.emit("data",n),t.read(0)):(e.length+=e.objectMode?1:n.length,r?e.buffer.unshift(n):e.buffer.push(n),e.needReadable&&y(t)),b(t,e)}function f(t,e){var n;return a(e)||"string"==typeof e||void 0===e||t.objectMode||(n=new TypeError("Invalid non-string/buffer chunk")),n}function h(t){return!t.ended&&(t.needReadable||t.length<t.highWaterMark||0===t.length)}function d(t){return t>=W?t=W:(t--,t|=t>>>1,t|=t>>>2,t|=t>>>4,t|=t>>>8,t|=t>>>16,t++),t}function p(t,e){return t<=0||0===e.length&&e.ended?0:e.objectMode?1:t!==t?e.flowing&&e.length?e.buffer.head.data.length:e.length:(t>e.highWaterMark&&(e.highWaterMark=d(t)),t<=e.length?t:e.ended?e.length:(e.needReadable=!0,0))}function g(t,e){if(!e.ended){if(e.decoder){var n=e.decoder.end();n&&n.length&&(e.buffer.push(n),e.length+=e.objectMode?1:n.length)}e.ended=!0,y(t)}}function y(t){var e=t._readableState;e.needReadable=!1,e.emittedReadable||(j("emitReadable",e.flowing),e.emittedReadable=!0,e.sync?D(m,t):m(t))}function m(t){j("emit readable"),t.emit("readable"),A(t)}function b(t,e){e.readingMore||(e.readingMore=!0,D(v,t,e))}function v(t,e){for(var n=e.length;!e.reading&&!e.flowing&&!e.ended&&e.length<e.highWaterMark&&(j("maybeReadMore read 0"),t.read(0),n!==e.length);)n=e.length;e.readingMore=!1}function E(t){return function(){var e=t._readableState;j("pipeOnDrain",e.awaitDrain),e.awaitDrain&&e.awaitDrain--,0===e.awaitDrain&&L(t,"data")&&(e.flowing=!0,A(t))}}function w(t){j("readable nexttick read 0"),t.read(0)}function _(t,e){e.resumeScheduled||(e.resumeScheduled=!0,D(T,t,e))}function T(t,e){e.reading||(j("resume read 0"),t.read(0)),e.resumeScheduled=!1,e.awaitDrain=0,t.emit("resume"),A(t),e.flowing&&!e.reading&&t.read(0)}function A(t){var e=t._readableState;for(j("flow",e.flowing);e.flowing&&null!==t.read(););}function x(t,e){if(0===e.length)return null;var n;return e.objectMode?n=e.buffer.shift():!t||t>=e.length?(n=e.decoder?e.buffer.join(""):1===e.buffer.length?e.buffer.head.data:e.buffer.concat(e.length),e.buffer.clear()):n=S(t,e.buffer,e.decoder),n}function S(t,e,n){var r;return t<e.head.data.length?(r=e.head.data.slice(0,t),e.head.data=e.head.data.slice(t)):r=t===e.head.data.length?e.shift():n?N(t,e):I(t,e),r}function N(t,e){var n=e.head,r=1,i=n.data;for(t-=i.length;n=n.next;){var a=n.data,o=t>a.length?a.length:t;if(o===a.length?i+=a:i+=a.slice(0,t),0===(t-=o)){o===a.length?(++r,n.next?e.head=n.next:e.head=e.tail=null):(e.head=n,n.data=a.slice(o));break}++r}return e.length-=r,i}function I(t,e){var n=B.allocUnsafe(t),r=e.head,i=1;for(r.data.copy(n),t-=r.data.length;r=r.next;){var a=r.data,o=t>a.length?a.length:t;if(a.copy(n,n.length-t,0,o),0===(t-=o)){o===a.length?(++i,r.next?e.head=r.next:e.head=e.tail=null):(e.head=r,r.data=a.slice(o));break}++i}return e.length-=i,n}function C(t){var e=t._readableState;if(e.length>0)throw new Error('"endReadable()" called on non-empty stream');e.endEmitted||(e.ended=!0,D(F,e,t))}function F(t,e){t.endEmitted||0!==t.length||(t.endEmitted=!0,e.readable=!1,e.emit("end"))}function R(t,e){for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return n;return-1}var D=n(6);t.exports=u;var O,P=n(15);u.ReadableState=s;var L=(n(8).EventEmitter,function(t,e){return t.listeners(e).length}),k=n(17),B=n(7).Buffer,M=e.Uint8Array||function(){},U=n(3);U.inherits=n(1);var K=n(26),j=void 0;j=K&&K.debuglog?K.debuglog("stream"):function(){};var Y,V=n(27),q=n(18);U.inherits(u,k);var G=["error","close","destroy","pause","resume"];Object.defineProperty(u.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(t){this._readableState&&(this._readableState.destroyed=t)}}),u.prototype.destroy=q.destroy,u.prototype._undestroy=q.undestroy,u.prototype._destroy=function(t,e){this.push(null),e(t)},u.prototype.push=function(t,e){var n,r=this._readableState;return r.objectMode?n=!0:"string"==typeof t&&(e=e||r.defaultEncoding,e!==r.encoding&&(t=B.from(t,e),e=""),n=!0),c(this,t,e,!1,n)},u.prototype.unshift=function(t){return c(this,t,null,!0,!1)},u.prototype.isPaused=function(){return!1===this._readableState.flowing},u.prototype.setEncoding=function(t){return Y||(Y=n(11).StringDecoder),this._readableState.decoder=new Y(t),this._readableState.encoding=t,this};var W=8388608;u.prototype.read=function(t){j("read",t),t=parseInt(t,10);var e=this._readableState,n=t;if(0!==t&&(e.emittedReadable=!1),0===t&&e.needReadable&&(e.length>=e.highWaterMark||e.ended))return j("read: emitReadable",e.length,e.ended),0===e.length&&e.ended?C(this):y(this),null;if(0===(t=p(t,e))&&e.ended)return 0===e.length&&C(this),null;var r=e.needReadable;j("need readable",r),(0===e.length||e.length-t<e.highWaterMark)&&(r=!0,j("length less than watermark",r)),e.ended||e.reading?(r=!1,j("reading or ended",r)):r&&(j("do read"),e.reading=!0,e.sync=!0,0===e.length&&(e.needReadable=!0),this._read(e.highWaterMark),e.sync=!1,e.reading||(t=p(n,e)));var i;return i=t>0?x(t,e):null,null===i?(e.needReadable=!0,t=0):e.length-=t,0===e.length&&(e.ended||(e.needReadable=!0),n!==t&&e.ended&&C(this)),null!==i&&this.emit("data",i),i},u.prototype._read=function(t){this.emit("error",new Error("_read() is not implemented"))},u.prototype.pipe=function(t,e){function n(t,e){j("onunpipe"),t===h&&e&&!1===e.hasUnpiped&&(e.hasUnpiped=!0,a())}function i(){j("onend"),t.end()}function a(){j("cleanup"),t.removeListener("close",c),t.removeListener("finish",l),t.removeListener("drain",y),t.removeListener("error",u),t.removeListener("unpipe",n),h.removeListener("end",i),h.removeListener("end",f),h.removeListener("data",s),m=!0,!d.awaitDrain||t._writableState&&!t._writableState.needDrain||y()}function s(e){j("ondata"),b=!1,!1!==t.write(e)||b||((1===d.pipesCount&&d.pipes===t||d.pipesCount>1&&-1!==R(d.pipes,t))&&!m&&(j("false write response, pause",h._readableState.awaitDrain),h._readableState.awaitDrain++,b=!0),h.pause())}function u(e){j("onerror",e),f(),t.removeListener("error",u),0===L(t,"error")&&t.emit("error",e)}function c(){t.removeListener("finish",l),f()}function l(){j("onfinish"),t.removeListener("close",c),f()}function f(){j("unpipe"),h.unpipe(t)}var h=this,d=this._readableState;switch(d.pipesCount){case 0:d.pipes=t;break;case 1:d.pipes=[d.pipes,t];break;default:d.pipes.push(t)}d.pipesCount+=1,j("pipe count=%d opts=%j",d.pipesCount,e);var p=(!e||!1!==e.end)&&t!==r.stdout&&t!==r.stderr,g=p?i:f;d.endEmitted?D(g):h.once("end",g),t.on("unpipe",n);var y=E(h);t.on("drain",y);var m=!1,b=!1;return h.on("data",s),o(t,"error",u),t.once("close",c),t.once("finish",l),t.emit("pipe",h),d.flowing||(j("pipe resume"),h.resume()),t},u.prototype.unpipe=function(t){var e=this._readableState,n={hasUnpiped:!1};if(0===e.pipesCount)return this;if(1===e.pipesCount)return t&&t!==e.pipes?this:(t||(t=e.pipes),e.pipes=null,e.pipesCount=0,e.flowing=!1,t&&t.emit("unpipe",this,n),this);if(!t){var r=e.pipes,i=e.pipesCount;e.pipes=null,e.pipesCount=0,e.flowing=!1;for(var a=0;a<i;a++)r[a].emit("unpipe",this,n);return this}var o=R(e.pipes,t);return-1===o?this:(e.pipes.splice(o,1),e.pipesCount-=1,1===e.pipesCount&&(e.pipes=e.pipes[0]),t.emit("unpipe",this,n),this)},u.prototype.on=function(t,e){var n=k.prototype.on.call(this,t,e);if("data"===t)!1!==this._readableState.flowing&&this.resume();else if("readable"===t){var r=this._readableState;r.endEmitted||r.readableListening||(r.readableListening=r.needReadable=!0,r.emittedReadable=!1,r.reading?r.length&&y(this):D(w,this))}return n},u.prototype.addListener=u.prototype.on,u.prototype.resume=function(){var t=this._readableState;return t.flowing||(j("resume"),t.flowing=!0,_(this,t)),this},u.prototype.pause=function(){return j("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(j("pause"),this._readableState.flowing=!1,this.emit("pause")),this},u.prototype.wrap=function(t){var e=this._readableState,n=!1,r=this;t.on("end",function(){if(j("wrapped end"),e.decoder&&!e.ended){var t=e.decoder.end();t&&t.length&&r.push(t)}r.push(null)}),t.on("data",function(i){if(j("wrapped data"),e.decoder&&(i=e.decoder.write(i)),(!e.objectMode||null!==i&&void 0!==i)&&(e.objectMode||i&&i.length)){r.push(i)||(n=!0,t.pause())}});for(var i in t)void 0===this[i]&&"function"==typeof t[i]&&(this[i]=function(e){return function(){return t[e].apply(t,arguments)}}(i));for(var a=0;a<G.length;a++)t.on(G[a],r.emit.bind(r,G[a]));return r._read=function(e){j("wrapped _read",e),n&&(n=!1,t.resume())},r},u._fromList=x}).call(e,n(2),n(5))},function(t,e,n){t.exports=n(8).EventEmitter},function(t,e,n){"use strict";function r(t,e){var n=this,r=this._readableState&&this._readableState.destroyed,i=this._writableState&&this._writableState.destroyed;if(r||i)return void(e?e(t):!t||this._writableState&&this._writableState.errorEmitted||o(a,this,t));this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(t||null,function(t){!e&&t?(o(a,n,t),n._writableState&&(n._writableState.errorEmitted=!0)):e&&e(t)})}function i(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}function a(t,e){t.emit("error",e)}var o=n(6);t.exports={destroy:r,undestroy:i}},function(t,e,n){"use strict";function r(t){this.afterTransform=function(e,n){return i(t,e,n)},this.needTransform=!1,this.transforming=!1,this.writecb=null,this.writechunk=null,this.writeencoding=null}function i(t,e,n){var r=t._transformState;r.transforming=!1;var i=r.writecb;if(!i)return t.emit("error",new Error("write callback called multiple times"));r.writechunk=null,r.writecb=null,null!==n&&void 0!==n&&t.push(n),i(e);var a=t._readableState;a.reading=!1,(a.needReadable||a.length<a.highWaterMark)&&t._read(a.highWaterMark)}function a(t){if(!(this instanceof a))return new a(t);s.call(this,t),this._transformState=new r(this);var e=this;this._readableState.needReadable=!0,this._readableState.sync=!1,t&&("function"==typeof t.transform&&(this._transform=t.transform),"function"==typeof t.flush&&(this._flush=t.flush)),this.once("prefinish",function(){"function"==typeof this._flush?this._flush(function(t,n){o(e,t,n)}):o(e)})}function o(t,e,n){if(e)return t.emit("error",e);null!==n&&void 0!==n&&t.push(n);var r=t._writableState,i=t._transformState;if(r.length)throw new Error("Calling transform done when ws.length != 0");if(i.transforming)throw new Error("Calling transform done when still transforming");return t.push(null)}t.exports=a;var s=n(0),u=n(3);u.inherits=n(1),u.inherits(a,s),a.prototype.push=function(t,e){return this._transformState.needTransform=!1,s.prototype.push.call(this,t,e)},a.prototype._transform=function(t,e,n){throw new Error("_transform() is not implemented")},a.prototype._write=function(t,e,n){var r=this._transformState;if(r.writecb=n,r.writechunk=t,r.writeencoding=e,!r.transforming){var i=this._readableState;(r.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},a.prototype._read=function(t){var e=this._transformState;null!==e.writechunk&&e.writecb&&!e.transforming?(e.transforming=!0,this._transform(e.writechunk,e.writeencoding,e.afterTransform)):e.needTransform=!0},a.prototype._destroy=function(t,e){var n=this;s.prototype._destroy.call(this,t,function(t){e(t),n.emit("close")})}},function(t,e,n){function r(t){var e=E.copyOptions(t);return E.ensureFlagExists("ignoreDeclaration",e),E.ensureFlagExists("ignoreInstruction",e),E.ensureFlagExists("ignoreAttributes",e),E.ensureFlagExists("ignoreText",e),E.ensureFlagExists("ignoreComment",e),E.ensureFlagExists("ignoreCdata",e),E.ensureFlagExists("ignoreDoctype",e),E.ensureFlagExists("compact",e),E.ensureFlagExists("indentText",e),E.ensureFlagExists("indentCdata",e),E.ensureFlagExists("indentAttributes",e),E.ensureFlagExists("indentInstruction",e),E.ensureFlagExists("fullTagEmptyElement",e),E.ensureFlagExists("noQuotesForNativeAttributes",e),E.ensureSpacesExists(e),"number"==typeof e.spaces&&(e.spaces=Array(e.spaces+1).join(" ")),E.ensureKeyExists("declaration",e),E.ensureKeyExists("instruction",e),E.ensureKeyExists("attributes",e),E.ensureKeyExists("text",e),E.ensureKeyExists("comment",e),E.ensureKeyExists("cdata",e),E.ensureKeyExists("doctype",e),E.ensureKeyExists("type",e),E.ensureKeyExists("name",e),E.ensureKeyExists("elements",e),E.checkFnExists("doctype",e),E.checkFnExists("instruction",e),E.checkFnExists("cdata",e),E.checkFnExists("comment",e),E.checkFnExists("text",e),E.checkFnExists("instructionName",e),E.checkFnExists("elementName",e),E.checkFnExists("attributeName",e),E.checkFnExists("attributeValue",e),E.checkFnExists("attributes",e),E.checkFnExists("fullTagEmptyElement",e),e}function i(t,e,n){return(!n&&t.spaces?"\n":"")+Array(e+1).join(t.spaces)}function a(t,e,n){if(e.ignoreAttributes)return"";"attributesFn"in e&&(t=e.attributesFn(t,v,b));var r,a,o,s,u="";for(r in t)t.hasOwnProperty(r)&&(s=e.noQuotesForNativeAttributes&&"string"!=typeof t[r]?"":'"',a=""+t[r],a=a.replace(/"/g,"&quot;"),o="attributeNameFn"in e?e.attributeNameFn(r,a,v,b):r,u+=e.spaces&&e.indentAttributes?i(e,n+1,!1):" ",u+=o+"="+s+("attributeValueFn"in e?e.attributeValueFn(a,r,v,b):a)+s);return t&&Object.keys(t).length&&e.spaces&&e.indentAttributes&&(u+=i(e,n,!1)),u}function o(t,e,n){return b=t,v="xml",e.ignoreDeclaration?"":"<?xml"+a(t[e.attributesKey],e,n)+"?>"}function s(t,e,n){if(e.ignoreInstruction)return"";var r;for(r in t)if(t.hasOwnProperty(r))break;var i="instructionNameFn"in e?e.instructionNameFn(r,t[r],v,b):r;if("object"==typeof t[r])return b=t,v=i,"<?"+i+a(t[r][e.attributesKey],e,n)+"?>";var o=t[r]?t[r]:"";return"instructionFn"in e&&(o=e.instructionFn(o,r,v,b)),"<?"+i+(o?" "+o:"")+"?>"}function u(t,e){return e.ignoreComment?"":"\x3c!--"+("commentFn"in e?e.commentFn(t,v,b):t)+"--\x3e"}function c(t,e){return e.ignoreCdata?"":"<![CDATA["+("cdataFn"in e?e.cdataFn(t,v,b):t)+"]]>"}function l(t,e){return e.ignoreDoctype?"":"<!DOCTYPE "+("doctypeFn"in e?e.doctypeFn(t,v,b):t)+">"}function f(t,e){return e.ignoreText?"":(t=""+t,t=t.replace(/&amp;/g,"&"),t=t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),"textFn"in e?e.textFn(t,v,b):t)}function h(t,e){var n;if(t.elements&&t.elements.length)for(n=0;n<t.elements.length;++n)switch(t.elements[n][e.typeKey]){case"text":if(e.indentText)return!0;break;case"cdata":if(e.indentCdata)return!0;break;case"instruction":if(e.indentInstruction)return!0;break;case"doctype":case"comment":case"element":default:return!0}return!1}function d(t,e,n){b=t,v=t.name;var r="",i="elementNameFn"in e?e.elementNameFn(t.name,t):t.name;r+="<"+i,t[e.attributesKey]&&(r+=a(t[e.attributesKey],e,n));var o=t[e.elementsKey]&&t[e.elementsKey].length||t[e.attributesKey]&&"preserve"===t[e.attributesKey]["xml:space"];return o||(o="fullTagEmptyElementFn"in e?e.fullTagEmptyElementFn(t.name,t):e.fullTagEmptyElement),o?(r+=">",t[e.elementsKey]&&t[e.elementsKey].length&&(r+=p(t[e.elementsKey],e,n+1),b=t,v=t.name),r+=e.spaces&&h(t,e)?"\n"+Array(n+1).join(e.spaces):"",r+="</"+i+">"):r+="/>",r}function p(t,e,n,r){return t.reduce(function(t,a){var o=i(e,n,r&&!t);switch(a.type){case"element":return t+o+d(a,e,n);case"comment":return t+o+u(a[e.commentKey],e);case"doctype":return t+o+l(a[e.doctypeKey],e);case"cdata":return t+(e.indentCdata?o:"")+c(a[e.cdataKey],e);case"text":return t+(e.indentText?o:"")+f(a[e.textKey],e);case"instruction":var h={};return h[a[e.nameKey]]=a[e.attributesKey]?a:a[e.instructionKey],t+(e.indentInstruction?o:"")+s(h,e,n)}},"")}function g(t,e,n){var r;for(r in t)if(t.hasOwnProperty(r))switch(r){case e.parentKey:case e.attributesKey:break;case e.textKey:if(e.indentText||n)return!0;break;case e.cdataKey:if(e.indentCdata||n)return!0;break;case e.instructionKey:if(e.indentInstruction||n)return!0;break;case e.doctypeKey:case e.commentKey:default:return!0}return!1}function y(t,e,n,r,o){b=t,v=e;var s="elementNameFn"in n?n.elementNameFn(e,t):e;if(void 0===t||null===t)return"fullTagEmptyElementFn"in n&&n.fullTagEmptyElementFn(e,t)||n.fullTagEmptyElement?"<"+s+"></"+s+">":"<"+s+"/>";var u="";if(e){if(u+="<"+s,"object"!=typeof t)return u+=">"+f(t,n)+"</"+s+">";t[n.attributesKey]&&(u+=a(t[n.attributesKey],n,r));var c=g(t,n,!0)||t[n.attributesKey]&&"preserve"===t[n.attributesKey]["xml:space"];if(c||(c="fullTagEmptyElementFn"in n?n.fullTagEmptyElementFn(e,t):n.fullTagEmptyElement),!c)return u+="/>";u+=">"}return u+=m(t,n,r+1,!1),b=t,v=e,e&&(u+=(o?i(n,r,!1):"")+"</"+s+">"),u}function m(t,e,n,r){var a,h,d,p="";for(h in t)if(t.hasOwnProperty(h))for(d=w(t[h])?t[h]:[t[h]],a=0;a<d.length;++a){switch(h){case e.declarationKey:p+=o(d[a],e,n);break;case e.instructionKey:p+=(e.indentInstruction?i(e,n,r):"")+s(d[a],e,n);break;case e.attributesKey:case e.parentKey:break;case e.textKey:p+=(e.indentText?i(e,n,r):"")+f(d[a],e);break;case e.cdataKey:p+=(e.indentCdata?i(e,n,r):"")+c(d[a],e);break;case e.doctypeKey:p+=i(e,n,r)+l(d[a],e);break;case e.commentKey:p+=i(e,n,r)+u(d[a],e);break;default:p+=i(e,n,r)+y(d[a],h,e,n,g(d[a],e))}r=r&&!p}return p}var b,v,E=n(12),w=n(13).isArray;t.exports=function(t,e){e=r(e);var n="";return b=t,v="_root_",e.compact?n=m(t,e,0,!0):(t[e.declarationKey]&&(n+=o(t[e.declarationKey],e,0)),t[e.elementsKey]&&t[e.elementsKey].length&&(n+=p(t[e.elementsKey],e,0,!n))),n}},function(t,e,n){var r=n(14),i=n(36),a=n(20),o=n(37);t.exports={xml2js:r,xml2json:i,js2xml:a,json2xml:o}},function(t,e,n){(function(t){!function(e){function r(t,n){if(!(this instanceof r))return new r(t,n);var i=this;a(i),i.q=i.c="",i.bufferCheckPosition=e.MAX_BUFFER_LENGTH,i.opt=n||{},i.opt.lowercase=i.opt.lowercase||i.opt.lowercasetags,i.looseCase=i.opt.lowercase?"toLowerCase":"toUpperCase",i.tags=[],i.closed=i.closedRoot=i.sawRoot=!1,i.tag=i.error=null,i.strict=!!t,i.noscript=!(!t&&!i.opt.noscript),i.state=Y.BEGIN,i.strictEntities=i.opt.strictEntities,i.ENTITIES=i.strictEntities?Object.create(e.XML_ENTITIES):Object.create(e.ENTITIES),i.attribList=[],i.opt.xmlns&&(i.ns=Object.create(B)),i.trackPosition=!1!==i.opt.position,i.trackPosition&&(i.position=i.line=i.column=0),p(i,"onready")}function i(t){for(var n=Math.max(e.MAX_BUFFER_LENGTH,10),r=0,i=0,a=F.length;i<a;i++){var o=t[F[i]].length;if(o>n)switch(F[i]){case"textNode":y(t);break;case"cdata":g(t,"oncdata",t.cdata),t.cdata="";break;case"script":g(t,"onscript",t.script),t.script="";break;default:b(t,"Max buffer length exceeded: "+F[i])}r=Math.max(r,o)}var s=e.MAX_BUFFER_LENGTH-r;t.bufferCheckPosition=s+t.position}function a(t){for(var e=0,n=F.length;e<n;e++)t[F[e]]=""}function o(t){y(t),""!==t.cdata&&(g(t,"oncdata",t.cdata),t.cdata=""),""!==t.script&&(g(t,"onscript",t.script),t.script="")}function s(t,e){return new u(t,e)}function u(t,e){if(!(this instanceof u))return new u(t,e);R.apply(this),this._parser=new r(t,e),this.writable=!0,this.readable=!0;var n=this;this._parser.onend=function(){n.emit("end")},this._parser.onerror=function(t){n.emit("error",t),n._parser.error=null},this._decoder=null,D.forEach(function(t){Object.defineProperty(n,"on"+t,{get:function(){return n._parser["on"+t]},set:function(e){if(!e)return n.removeAllListeners(t),n._parser["on"+t]=e,e;n.on(t,e)},enumerable:!0,configurable:!1})})}function c(t){return" "===t||"\n"===t||"\r"===t||"\t"===t}function l(t){return'"'===t||"'"===t}function f(t){return">"===t||c(t)}function h(t,e){return t.test(e)}function d(t,e){return!h(t,e)}function p(t,e,n){t[e]&&t[e](n)}function g(t,e,n){t.textNode&&y(t),p(t,e,n)}function y(t){t.textNode=m(t.opt,t.textNode),t.textNode&&p(t,"ontext",t.textNode),t.textNode=""}function m(t,e){return t.trim&&(e=e.trim()),t.normalize&&(e=e.replace(/\s+/g," ")),e}function b(t,e){return y(t),t.trackPosition&&(e+="\nLine: "+t.line+"\nColumn: "+t.column+"\nChar: "+t.c),e=new Error(e),t.error=e,p(t,"onerror",e),t}function v(t){return t.sawRoot&&!t.closedRoot&&E(t,"Unclosed root tag"),t.state!==Y.BEGIN&&t.state!==Y.BEGIN_WHITESPACE&&t.state!==Y.TEXT&&b(t,"Unexpected end"),y(t),t.c="",t.closed=!0,p(t,"onend"),r.call(t,t.strict,t.opt),t}function E(t,e){if("object"!=typeof t||!(t instanceof r))throw new Error("bad call to strictFail");t.strict&&b(t,e)}function w(t){t.strict||(t.tagName=t.tagName[t.looseCase]());var e=t.tags[t.tags.length-1]||t,n=t.tag={name:t.tagName,attributes:{}};t.opt.xmlns&&(n.ns=e.ns),t.attribList.length=0,g(t,"onopentagstart",n)}function _(t,e){var n=t.indexOf(":"),r=n<0?["",t]:t.split(":"),i=r[0],a=r[1];return e&&"xmlns"===t&&(i="xmlns",a=""),{prefix:i,local:a}}function T(t){if(t.strict||(t.attribName=t.attribName[t.looseCase]()),-1!==t.attribList.indexOf(t.attribName)||t.tag.attributes.hasOwnProperty(t.attribName))return void(t.attribName=t.attribValue="");if(t.opt.xmlns){var e=_(t.attribName,!0),n=e.prefix,r=e.local;if("xmlns"===n)if("xml"===r&&t.attribValue!==L)E(t,"xml: prefix must be bound to "+L+"\nActual: "+t.attribValue);else if("xmlns"===r&&t.attribValue!==k)E(t,"xmlns: prefix must be bound to "+k+"\nActual: "+t.attribValue);else{var i=t.tag,a=t.tags[t.tags.length-1]||t;i.ns===a.ns&&(i.ns=Object.create(a.ns)),i.ns[r]=t.attribValue}t.attribList.push([t.attribName,t.attribValue])}else t.tag.attributes[t.attribName]=t.attribValue,g(t,"onattribute",{name:t.attribName,value:t.attribValue});t.attribName=t.attribValue=""}function A(t,e){if(t.opt.xmlns){var n=t.tag,r=_(t.tagName);n.prefix=r.prefix,n.local=r.local,n.uri=n.ns[r.prefix]||"",n.prefix&&!n.uri&&(E(t,"Unbound namespace prefix: "+JSON.stringify(t.tagName)),n.uri=r.prefix);var i=t.tags[t.tags.length-1]||t;n.ns&&i.ns!==n.ns&&Object.keys(n.ns).forEach(function(e){g(t,"onopennamespace",{prefix:e,uri:n.ns[e]})});for(var a=0,o=t.attribList.length;a<o;a++){var s=t.attribList[a],u=s[0],c=s[1],l=_(u,!0),f=l.prefix,h=l.local,d=""===f?"":n.ns[f]||"",p={name:u,value:c,prefix:f,local:h,uri:d};f&&"xmlns"!==f&&!d&&(E(t,"Unbound namespace prefix: "+JSON.stringify(f)),p.uri=f),t.tag.attributes[u]=p,g(t,"onattribute",p)}t.attribList.length=0}t.tag.isSelfClosing=!!e,t.sawRoot=!0,t.tags.push(t.tag),g(t,"onopentag",t.tag),e||(t.noscript||"script"!==t.tagName.toLowerCase()?t.state=Y.TEXT:t.state=Y.SCRIPT,t.tag=null,t.tagName=""),t.attribName=t.attribValue="",t.attribList.length=0}function x(t){if(!t.tagName)return E(t,"Weird empty close tag."),t.textNode+="</>",void(t.state=Y.TEXT);if(t.script){if("script"!==t.tagName)return t.script+="</"+t.tagName+">",t.tagName="",void(t.state=Y.SCRIPT);g(t,"onscript",t.script),t.script=""}var e=t.tags.length,n=t.tagName;t.strict||(n=n[t.looseCase]());for(var r=n;e--;){if(t.tags[e].name===r)break;E(t,"Unexpected close tag")}if(e<0)return E(t,"Unmatched closing tag: "+t.tagName),t.textNode+="</"+t.tagName+">",void(t.state=Y.TEXT);t.tagName=n;for(var i=t.tags.length;i-- >e;){var a=t.tag=t.tags.pop();t.tagName=t.tag.name,g(t,"onclosetag",t.tagName);var o={};for(var s in a.ns)o[s]=a.ns[s];var u=t.tags[t.tags.length-1]||t;t.opt.xmlns&&a.ns!==u.ns&&Object.keys(a.ns).forEach(function(e){var n=a.ns[e];g(t,"onclosenamespace",{prefix:e,uri:n})})}0===e&&(t.closedRoot=!0),t.tagName=t.attribValue=t.attribName="",t.attribList.length=0,t.state=Y.TEXT}function S(t){var e,n=t.entity,r=n.toLowerCase(),i="";return t.ENTITIES[n]?t.ENTITIES[n]:t.ENTITIES[r]?t.ENTITIES[r]:(n=r,"#"===n.charAt(0)&&("x"===n.charAt(1)?(n=n.slice(2),e=parseInt(n,16),i=e.toString(16)):(n=n.slice(1),e=parseInt(n,10),i=e.toString(10))),n=n.replace(/^0+/,""),isNaN(e)||i.toLowerCase()!==n?(E(t,"Invalid character entity"),"&"+t.entity+";"):String.fromCodePoint(e))}function N(t,e){"<"===e?(t.state=Y.OPEN_WAKA,t.startTagPosition=t.position):c(e)||(E(t,"Non-whitespace before first tag."),t.textNode=e,t.state=Y.TEXT)}function I(t,e){var n="";return e<t.length&&(n=t.charAt(e)),n}function C(t){var e=this;if(this.error)throw this.error;if(e.closed)return b(e,"Cannot write after close. Assign an onready handler.");if(null===t)return v(e);"object"==typeof t&&(t=t.toString());for(var n=0,r="";;){if(r=I(t,n++),e.c=r,!r)break;switch(e.trackPosition&&(e.position++,"\n"===r?(e.line++,e.column=0):e.column++),e.state){case Y.BEGIN:if(e.state=Y.BEGIN_WHITESPACE,"\ufeff"===r)continue;N(e,r);continue;case Y.BEGIN_WHITESPACE:N(e,r);continue;case Y.TEXT:if(e.sawRoot&&!e.closedRoot){for(var a=n-1;r&&"<"!==r&&"&"!==r;)(r=I(t,n++))&&e.trackPosition&&(e.position++,"\n"===r?(e.line++,e.column=0):e.column++);e.textNode+=t.substring(a,n-1)}"<"!==r||e.sawRoot&&e.closedRoot&&!e.strict?(c(r)||e.sawRoot&&!e.closedRoot||E(e,"Text data outside of root node."),"&"===r?e.state=Y.TEXT_ENTITY:e.textNode+=r):(e.state=Y.OPEN_WAKA,e.startTagPosition=e.position);continue;case Y.SCRIPT:"<"===r?e.state=Y.SCRIPT_ENDING:e.script+=r;continue;case Y.SCRIPT_ENDING:"/"===r?e.state=Y.CLOSE_TAG:(e.script+="<"+r,e.state=Y.SCRIPT);continue;case Y.OPEN_WAKA:if("!"===r)e.state=Y.SGML_DECL,e.sgmlDecl="";else if(c(r));else if(h(M,r))e.state=Y.OPEN_TAG,e.tagName=r;else if("/"===r)e.state=Y.CLOSE_TAG,e.tagName="";else if("?"===r)e.state=Y.PROC_INST,e.procInstName=e.procInstBody="";else{if(E(e,"Unencoded <"),e.startTagPosition+1<e.position){var o=e.position-e.startTagPosition;r=new Array(o).join(" ")+r}e.textNode+="<"+r,e.state=Y.TEXT}continue;case Y.SGML_DECL:(e.sgmlDecl+r).toUpperCase()===O?(g(e,"onopencdata"),e.state=Y.CDATA,e.sgmlDecl="",e.cdata=""):e.sgmlDecl+r==="--"?(e.state=Y.COMMENT,e.comment="",e.sgmlDecl=""):(e.sgmlDecl+r).toUpperCase()===P?(e.state=Y.DOCTYPE,(e.doctype||e.sawRoot)&&E(e,"Inappropriately located doctype declaration"),e.doctype="",e.sgmlDecl=""):">"===r?(g(e,"onsgmldeclaration",e.sgmlDecl),e.sgmlDecl="",e.state=Y.TEXT):l(r)?(e.state=Y.SGML_DECL_QUOTED,e.sgmlDecl+=r):e.sgmlDecl+=r;continue;case Y.SGML_DECL_QUOTED:r===e.q&&(e.state=Y.SGML_DECL,e.q=""),e.sgmlDecl+=r;continue;case Y.DOCTYPE:">"===r?(e.state=Y.TEXT,g(e,"ondoctype",e.doctype),e.doctype=!0):(e.doctype+=r,"["===r?e.state=Y.DOCTYPE_DTD:l(r)&&(e.state=Y.DOCTYPE_QUOTED,e.q=r));continue;case Y.DOCTYPE_QUOTED:e.doctype+=r,r===e.q&&(e.q="",e.state=Y.DOCTYPE);continue;case Y.DOCTYPE_DTD:e.doctype+=r,"]"===r?e.state=Y.DOCTYPE:l(r)&&(e.state=Y.DOCTYPE_DTD_QUOTED,e.q=r);continue;case Y.DOCTYPE_DTD_QUOTED:e.doctype+=r,r===e.q&&(e.state=Y.DOCTYPE_DTD,e.q="");continue;case Y.COMMENT:"-"===r?e.state=Y.COMMENT_ENDING:e.comment+=r;continue;case Y.COMMENT_ENDING:"-"===r?(e.state=Y.COMMENT_ENDED,e.comment=m(e.opt,e.comment),e.comment&&g(e,"oncomment",e.comment),e.comment=""):(e.comment+="-"+r,e.state=Y.COMMENT);continue;case Y.COMMENT_ENDED:">"!==r?(E(e,"Malformed comment"),e.comment+="--"+r,e.state=Y.COMMENT):e.state=Y.TEXT;continue;case Y.CDATA:"]"===r?e.state=Y.CDATA_ENDING:e.cdata+=r;continue;case Y.CDATA_ENDING:"]"===r?e.state=Y.CDATA_ENDING_2:(e.cdata+="]"+r,e.state=Y.CDATA);continue;case Y.CDATA_ENDING_2:">"===r?(e.cdata&&g(e,"oncdata",e.cdata),g(e,"onclosecdata"),e.cdata="",e.state=Y.TEXT):"]"===r?e.cdata+="]":(e.cdata+="]]"+r,e.state=Y.CDATA);continue;case Y.PROC_INST:"?"===r?e.state=Y.PROC_INST_ENDING:c(r)?e.state=Y.PROC_INST_BODY:e.procInstName+=r;continue;case Y.PROC_INST_BODY:if(!e.procInstBody&&c(r))continue;"?"===r?e.state=Y.PROC_INST_ENDING:e.procInstBody+=r;continue;case Y.PROC_INST_ENDING:">"===r?(g(e,"onprocessinginstruction",{name:e.procInstName,body:e.procInstBody}),e.procInstName=e.procInstBody="",e.state=Y.TEXT):(e.procInstBody+="?"+r,e.state=Y.PROC_INST_BODY);continue;case Y.OPEN_TAG:h(U,r)?e.tagName+=r:(w(e),">"===r?A(e):"/"===r?e.state=Y.OPEN_TAG_SLASH:(c(r)||E(e,"Invalid character in tag name"),e.state=Y.ATTRIB));continue;case Y.OPEN_TAG_SLASH:">"===r?(A(e,!0),x(e)):(E(e,"Forward-slash in opening tag not followed by >"),e.state=Y.ATTRIB);continue;case Y.ATTRIB:if(c(r))continue;">"===r?A(e):"/"===r?e.state=Y.OPEN_TAG_SLASH:h(M,r)?(e.attribName=r,e.attribValue="",e.state=Y.ATTRIB_NAME):E(e,"Invalid attribute name");continue;case Y.ATTRIB_NAME:"="===r?e.state=Y.ATTRIB_VALUE:">"===r?(E(e,"Attribute without value"),e.attribValue=e.attribName,T(e),A(e)):c(r)?e.state=Y.ATTRIB_NAME_SAW_WHITE:h(U,r)?e.attribName+=r:E(e,"Invalid attribute name");continue;case Y.ATTRIB_NAME_SAW_WHITE:if("="===r)e.state=Y.ATTRIB_VALUE;else{if(c(r))continue;E(e,"Attribute without value"),e.tag.attributes[e.attribName]="",e.attribValue="",g(e,"onattribute",{name:e.attribName,value:""}),e.attribName="",">"===r?A(e):h(M,r)?(e.attribName=r,e.state=Y.ATTRIB_NAME):(E(e,"Invalid attribute name"),e.state=Y.ATTRIB)}continue;case Y.ATTRIB_VALUE:if(c(r))continue;l(r)?(e.q=r,e.state=Y.ATTRIB_VALUE_QUOTED):(E(e,"Unquoted attribute value"),e.state=Y.ATTRIB_VALUE_UNQUOTED,e.attribValue=r);continue;case Y.ATTRIB_VALUE_QUOTED:if(r!==e.q){"&"===r?e.state=Y.ATTRIB_VALUE_ENTITY_Q:e.attribValue+=r;continue}T(e),e.q="",e.state=Y.ATTRIB_VALUE_CLOSED;continue;case Y.ATTRIB_VALUE_CLOSED:c(r)?e.state=Y.ATTRIB:">"===r?A(e):"/"===r?e.state=Y.OPEN_TAG_SLASH:h(M,r)?(E(e,"No whitespace between attributes"),e.attribName=r,e.attribValue="",e.state=Y.ATTRIB_NAME):E(e,"Invalid attribute name");continue;case Y.ATTRIB_VALUE_UNQUOTED:if(!f(r)){"&"===r?e.state=Y.ATTRIB_VALUE_ENTITY_U:e.attribValue+=r;continue}T(e),">"===r?A(e):e.state=Y.ATTRIB;continue;case Y.CLOSE_TAG:if(e.tagName)">"===r?x(e):h(U,r)?e.tagName+=r:e.script?(e.script+="</"+e.tagName,e.tagName="",e.state=Y.SCRIPT):(c(r)||E(e,"Invalid tagname in closing tag"),e.state=Y.CLOSE_TAG_SAW_WHITE);else{if(c(r))continue;d(M,r)?e.script?(e.script+="</"+r,e.state=Y.SCRIPT):E(e,"Invalid tagname in closing tag."):e.tagName=r}continue;case Y.CLOSE_TAG_SAW_WHITE:if(c(r))continue;">"===r?x(e):E(e,"Invalid characters in closing tag");continue;case Y.TEXT_ENTITY:case Y.ATTRIB_VALUE_ENTITY_Q:case Y.ATTRIB_VALUE_ENTITY_U:var s,u;switch(e.state){case Y.TEXT_ENTITY:s=Y.TEXT,u="textNode";break;case Y.ATTRIB_VALUE_ENTITY_Q:s=Y.ATTRIB_VALUE_QUOTED,u="attribValue";break;case Y.ATTRIB_VALUE_ENTITY_U:s=Y.ATTRIB_VALUE_UNQUOTED,u="attribValue"}";"===r?(e[u]+=S(e),e.entity="",e.state=s):h(e.entity.length?j:K,r)?e.entity+=r:(E(e,"Invalid character in entity name"),e[u]+="&"+e.entity+r,e.entity="",e.state=s);continue;default:throw new Error(e,"Unknown state: "+e.state)}}return e.position>=e.bufferCheckPosition&&i(e),e}e.parser=function(t,e){return new r(t,e)},e.SAXParser=r,e.SAXStream=u,e.createStream=s,e.MAX_BUFFER_LENGTH=65536;var F=["comment","sgmlDecl","textNode","tagName","doctype","procInstName","procInstBody","entity","attribName","attribValue","cdata","script"];e.EVENTS=["text","processinginstruction","sgmldeclaration","doctype","comment","opentagstart","attribute","opentag","closetag","opencdata","cdata","closecdata","error","end","ready","script","opennamespace","closenamespace"],Object.create||(Object.create=function(t){function e(){}return e.prototype=t,new e}),Object.keys||(Object.keys=function(t){var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n);return e}),r.prototype={end:function(){v(this)},write:C,resume:function(){return this.error=null,this},close:function(){return this.write(null)},flush:function(){o(this)}};var R;try{R=n(25).Stream}catch(t){R=function(){}}var D=e.EVENTS.filter(function(t){return"error"!==t&&"end"!==t});u.prototype=Object.create(R.prototype,{constructor:{value:u}}),u.prototype.write=function(e){if("function"==typeof t&&"function"==typeof t.isBuffer&&t.isBuffer(e)){if(!this._decoder){var r=n(11).StringDecoder;this._decoder=new r("utf8")}e=this._decoder.write(e)}return this._parser.write(e.toString()),this.emit("data",e),!0},u.prototype.end=function(t){return t&&t.length&&this.write(t),this._parser.end(),!0},u.prototype.on=function(t,e){var n=this;return n._parser["on"+t]||-1===D.indexOf(t)||(n._parser["on"+t]=function(){var e=1===arguments.length?[arguments[0]]:Array.apply(null,arguments);e.splice(0,0,t),n.emit.apply(n,e)}),R.prototype.on.call(n,t,e)};var O="[CDATA[",P="DOCTYPE",L="http://www.w3.org/XML/1998/namespace",k="http://www.w3.org/2000/xmlns/",B={xml:L,xmlns:k},M=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,U=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/,K=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,j=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/,Y=0;e.STATE={BEGIN:Y++,BEGIN_WHITESPACE:Y++,TEXT:Y++,TEXT_ENTITY:Y++,OPEN_WAKA:Y++,SGML_DECL:Y++,SGML_DECL_QUOTED:Y++,DOCTYPE:Y++,DOCTYPE_QUOTED:Y++,DOCTYPE_DTD:Y++,DOCTYPE_DTD_QUOTED:Y++,COMMENT_STARTING:Y++,COMMENT:Y++,COMMENT_ENDING:Y++,COMMENT_ENDED:Y++,CDATA:Y++,CDATA_ENDING:Y++,CDATA_ENDING_2:Y++,PROC_INST:Y++,PROC_INST_BODY:Y++,PROC_INST_ENDING:Y++,OPEN_TAG:Y++,OPEN_TAG_SLASH:Y++,ATTRIB:Y++,ATTRIB_NAME:Y++,ATTRIB_NAME_SAW_WHITE:Y++,ATTRIB_VALUE:Y++,ATTRIB_VALUE_QUOTED:Y++,ATTRIB_VALUE_CLOSED:Y++,ATTRIB_VALUE_UNQUOTED:Y++,ATTRIB_VALUE_ENTITY_Q:Y++,ATTRIB_VALUE_ENTITY_U:Y++,CLOSE_TAG:Y++,CLOSE_TAG_SAW_WHITE:Y++,SCRIPT:Y++,SCRIPT_ENDING:Y++},e.XML_ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'"},e.ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'",AElig:198,Aacute:193,Acirc:194,Agrave:192,Aring:197,Atilde:195,Auml:196,Ccedil:199,ETH:208,Eacute:201,Ecirc:202,Egrave:200,Euml:203,Iacute:205,Icirc:206,Igrave:204,Iuml:207,Ntilde:209,Oacute:211,Ocirc:212,Ograve:210,Oslash:216,Otilde:213,Ouml:214,THORN:222,Uacute:218,Ucirc:219,Ugrave:217,Uuml:220,Yacute:221,aacute:225,acirc:226,aelig:230,agrave:224,aring:229,atilde:227,auml:228,ccedil:231,eacute:233,ecirc:234,egrave:232,eth:240,euml:235,iacute:237,icirc:238,igrave:236,iuml:239,ntilde:241,oacute:243,ocirc:244,ograve:242,oslash:248,otilde:245,ouml:246,szlig:223,thorn:254,uacute:250,ucirc:251,ugrave:249,uuml:252,yacute:253,yuml:255,copy:169,reg:174,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,ordf:170,laquo:171,not:172,shy:173,macr:175,deg:176,plusmn:177,sup1:185,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,times:215,divide:247,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,fnof:402,circ:710,tilde:732,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,bull:8226,hellip:8230,permil:8240,prime:8242,Prime:8243,lsaquo:8249,rsaquo:8250,oline:8254,frasl:8260,euro:8364,image:8465,weierp:8472,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,int:8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},Object.keys(e.ENTITIES).forEach(function(t){var n=e.ENTITIES[t],r="number"==typeof n?String.fromCharCode(n):n;e.ENTITIES[t]=r});for(var V in e.STATE)e.STATE[e.STATE[V]]=V;Y=e.STATE,/*! http://mths.be/fromcodepoint v0.1.0 by @mathias */
String.fromCodePoint||function(){var t=String.fromCharCode,e=Math.floor,n=function(){var n,r,i=[],a=-1,o=arguments.length;if(!o)return"";for(var s="";++a<o;){var u=Number(arguments[a]);if(!isFinite(u)||u<0||u>1114111||e(u)!==u)throw RangeError("Invalid code point: "+u);u<=65535?i.push(u):(u-=65536,n=55296+(u>>10),r=u%1024+56320,i.push(n,r)),(a+1===o||i.length>16384)&&(s+=t.apply(null,i),i.length=0)}return s};Object.defineProperty?Object.defineProperty(String,"fromCodePoint",{value:n,configurable:!0,writable:!0}):String.fromCodePoint=n}()}(e)}).call(e,n(4).Buffer)},function(t,e,n){"use strict";function r(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");return"="===t[e-2]?2:"="===t[e-1]?1:0}function i(t){return 3*t.length/4-r(t)}function a(t){var e,n,i,a,o,s=t.length;a=r(t),o=new f(3*s/4-a),n=a>0?s-4:s;var u=0;for(e=0;e<n;e+=4)i=l[t.charCodeAt(e)]<<18|l[t.charCodeAt(e+1)]<<12|l[t.charCodeAt(e+2)]<<6|l[t.charCodeAt(e+3)],o[u++]=i>>16&255,o[u++]=i>>8&255,o[u++]=255&i;return 2===a?(i=l[t.charCodeAt(e)]<<2|l[t.charCodeAt(e+1)]>>4,o[u++]=255&i):1===a&&(i=l[t.charCodeAt(e)]<<10|l[t.charCodeAt(e+1)]<<4|l[t.charCodeAt(e+2)]>>2,o[u++]=i>>8&255,o[u++]=255&i),o}function o(t){return c[t>>18&63]+c[t>>12&63]+c[t>>6&63]+c[63&t]}function s(t,e,n){for(var r,i=[],a=e;a<n;a+=3)r=(t[a]<<16)+(t[a+1]<<8)+t[a+2],i.push(o(r));return i.join("")}function u(t){for(var e,n=t.length,r=n%3,i="",a=[],o=0,u=n-r;o<u;o+=16383)a.push(s(t,o,o+16383>u?u:o+16383));return 1===r?(e=t[n-1],i+=c[e>>2],i+=c[e<<4&63],i+="=="):2===r&&(e=(t[n-2]<<8)+t[n-1],i+=c[e>>10],i+=c[e>>4&63],i+=c[e<<2&63],i+="="),a.push(i),a.join("")}e.byteLength=i,e.toByteArray=a,e.fromByteArray=u;for(var c=[],l=[],f="undefined"!=typeof Uint8Array?Uint8Array:Array,h="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",d=0,p=h.length;d<p;++d)c[d]=h[d],l[h.charCodeAt(d)]=d;l["-".charCodeAt(0)]=62,l["_".charCodeAt(0)]=63},function(t,e){e.read=function(t,e,n,r,i){var a,o,s=8*i-r-1,u=(1<<s)-1,c=u>>1,l=-7,f=n?i-1:0,h=n?-1:1,d=t[e+f];for(f+=h,a=d&(1<<-l)-1,d>>=-l,l+=s;l>0;a=256*a+t[e+f],f+=h,l-=8);for(o=a&(1<<-l)-1,a>>=-l,l+=r;l>0;o=256*o+t[e+f],f+=h,l-=8);if(0===a)a=1-c;else{if(a===u)return o?NaN:1/0*(d?-1:1);o+=Math.pow(2,r),a-=c}return(d?-1:1)*o*Math.pow(2,a-r)},e.write=function(t,e,n,r,i,a){var o,s,u,c=8*a-i-1,l=(1<<c)-1,f=l>>1,h=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=r?0:a-1,p=r?1:-1,g=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(s=isNaN(e)?1:0,o=l):(o=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-o))<1&&(o--,u*=2),e+=o+f>=1?h/u:h*Math.pow(2,1-f),e*u>=2&&(o++,u/=2),o+f>=l?(s=0,o=l):o+f>=1?(s=(e*u-1)*Math.pow(2,i),o+=f):(s=e*Math.pow(2,f-1)*Math.pow(2,i),o=0));i>=8;t[n+d]=255&s,d+=p,s/=256,i-=8);for(o=o<<i|s,c+=i;c>0;t[n+d]=255&o,d+=p,o/=256,c-=8);t[n+d-p]|=128*g}},function(t,e,n){function r(){i.call(this)}t.exports=r;var i=n(8).EventEmitter;n(1)(r,i),r.Readable=n(9),r.Writable=n(32),r.Duplex=n(33),r.Transform=n(34),r.PassThrough=n(35),r.Stream=r,r.prototype.pipe=function(t,e){function n(e){t.writable&&!1===t.write(e)&&c.pause&&c.pause()}function r(){c.readable&&c.resume&&c.resume()}function a(){l||(l=!0,t.end())}function o(){l||(l=!0,"function"==typeof t.destroy&&t.destroy())}function s(t){if(u(),0===i.listenerCount(this,"error"))throw t}function u(){c.removeListener("data",n),t.removeListener("drain",r),c.removeListener("end",a),c.removeListener("close",o),c.removeListener("error",s),t.removeListener("error",s),c.removeListener("end",u),c.removeListener("close",u),t.removeListener("close",u)}var c=this;c.on("data",n),t.on("drain",r),t._isStdio||e&&!1===e.end||(c.on("end",a),c.on("close",o));var l=!1;return c.on("error",s),t.on("error",s),c.on("end",u),c.on("close",u),t.on("close",u),t.emit("pipe",c),t}},function(t,e){},function(t,e,n){"use strict";function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(t,e,n){t.copy(e,n)}var a=n(7).Buffer;t.exports=function(){function t(){r(this,t),this.head=null,this.tail=null,this.length=0}return t.prototype.push=function(t){var e={data:t,next:null};this.length>0?this.tail.next=e:this.head=e,this.tail=e,++this.length},t.prototype.unshift=function(t){var e={data:t,next:this.head};0===this.length&&(this.tail=e),this.head=e,++this.length},t.prototype.shift=function(){if(0!==this.length){var t=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,t}},t.prototype.clear=function(){this.head=this.tail=null,this.length=0},t.prototype.join=function(t){if(0===this.length)return"";for(var e=this.head,n=""+e.data;e=e.next;)n+=t+e.data;return n},t.prototype.concat=function(t){if(0===this.length)return a.alloc(0);if(1===this.length)return this.head.data;for(var e=a.allocUnsafe(t>>>0),n=this.head,r=0;n;)i(n.data,e,r),r+=n.data.length,n=n.next;return e},t}()},function(t,e,n){function r(t,e){this._id=t,this._clearFn=e}var i=Function.prototype.apply;e.setTimeout=function(){return new r(i.call(setTimeout,window,arguments),clearTimeout)},e.setInterval=function(){return new r(i.call(setInterval,window,arguments),clearInterval)},e.clearTimeout=e.clearInterval=function(t){t&&t.close()},r.prototype.unref=r.prototype.ref=function(){},r.prototype.close=function(){this._clearFn.call(window,this._id)},e.enroll=function(t,e){clearTimeout(t._idleTimeoutId),t._idleTimeout=e},e.unenroll=function(t){clearTimeout(t._idleTimeoutId),t._idleTimeout=-1},e._unrefActive=e.active=function(t){clearTimeout(t._idleTimeoutId);var e=t._idleTimeout;e>=0&&(t._idleTimeoutId=setTimeout(function(){t._onTimeout&&t._onTimeout()},e))},n(29),e.setImmediate=setImmediate,e.clearImmediate=clearImmediate},function(t,e,n){(function(t,e){!function(t,n){"use strict";function r(t){"function"!=typeof t&&(t=new Function(""+t));for(var e=new Array(arguments.length-1),n=0;n<e.length;n++)e[n]=arguments[n+1];var r={callback:t,args:e};return c[u]=r,s(u),u++}function i(t){delete c[t]}function a(t){var e=t.callback,r=t.args;switch(r.length){case 0:e();break;case 1:e(r[0]);break;case 2:e(r[0],r[1]);break;case 3:e(r[0],r[1],r[2]);break;default:e.apply(n,r)}}function o(t){if(l)setTimeout(o,0,t);else{var e=c[t];if(e){l=!0;try{a(e)}finally{i(t),l=!1}}}}if(!t.setImmediate){var s,u=1,c={},l=!1,f=t.document,h=Object.getPrototypeOf&&Object.getPrototypeOf(t);h=h&&h.setTimeout?h:t,"[object process]"==={}.toString.call(t.process)?function(){s=function(t){e.nextTick(function(){o(t)})}}():function(){if(t.postMessage&&!t.importScripts){var e=!0,n=t.onmessage;return t.onmessage=function(){e=!1},t.postMessage("","*"),t.onmessage=n,e}}()?function(){var e="setImmediate$"+Math.random()+"$",n=function(n){n.source===t&&"string"==typeof n.data&&0===n.data.indexOf(e)&&o(+n.data.slice(e.length))};t.addEventListener?t.addEventListener("message",n,!1):t.attachEvent("onmessage",n),s=function(n){t.postMessage(e+n,"*")}}():t.MessageChannel?function(){var t=new MessageChannel;t.port1.onmessage=function(t){o(t.data)},s=function(e){t.port2.postMessage(e)}}():f&&"onreadystatechange"in f.createElement("script")?function(){var t=f.documentElement;s=function(e){var n=f.createElement("script");n.onreadystatechange=function(){o(e),n.onreadystatechange=null,t.removeChild(n),n=null},t.appendChild(n)}}():function(){s=function(t){setTimeout(o,0,t)}}(),h.setImmediate=r,h.clearImmediate=i}}("undefined"==typeof self?void 0===t?this:t:self)}).call(e,n(2),n(5))},function(t,e,n){(function(e){function n(t,e){function n(){if(!i){if(r("throwDeprecation"))throw new Error(e);r("traceDeprecation")?console.trace(e):console.warn(e),i=!0}return t.apply(this,arguments)}if(r("noDeprecation"))return t;var i=!1;return n}function r(t){try{if(!e.localStorage)return!1}catch(t){return!1}var n=e.localStorage[t];return null!=n&&"true"===String(n).toLowerCase()}t.exports=n}).call(e,n(2))},function(t,e,n){"use strict";function r(t){if(!(this instanceof r))return new r(t);i.call(this,t)}t.exports=r;var i=n(19),a=n(3);a.inherits=n(1),a.inherits(r,i),r.prototype._transform=function(t,e,n){n(null,t)}},function(t,e,n){t.exports=n(10)},function(t,e,n){t.exports=n(0)},function(t,e,n){t.exports=n(9).Transform},function(t,e,n){t.exports=n(9).PassThrough},function(t,e,n){function r(t){var e=i.copyOptions(t);return i.ensureSpacesExists(e),e}var i=n(12),a=n(14);t.exports=function(t,e){var n,i,o,s;return n=r(e),i=a(t,n),s="compact"in n&&n.compact?"_parent":"parent",o="addParent"in n&&n.addParent?JSON.stringify(i,function(t,e){return t===s?"_":e},n.spaces):JSON.stringify(i,null,n.spaces),o.replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}},function(t,e,n){(function(e){var r=n(20);t.exports=function(t,n){t instanceof e&&(t=t.toString());var i=null;if("string"==typeof t)try{i=JSON.parse(t)}catch(t){throw new Error("The JSON structure is invalid")}else i=t;return r(i,n)}}).call(e,n(4).Buffer)}]));