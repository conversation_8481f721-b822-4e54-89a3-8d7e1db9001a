export type UniversalMeasure = `${"-" | ""}${number}${"mm" | "cm" | "in" | "pt" | "pc" | "pi"}`;
export type PositiveUniversalMeasure = `${number}${"mm" | "cm" | "in" | "pt" | "pc" | "pi"}`;
export type Percentage = `${"-" | ""}${number}%`;
export declare const decimalNumber: (val: number) => number;
export declare const unsignedDecimalNumber: (val: number) => number;
export declare const longHexNumber: (val: string) => string;
export declare const shortHexNumber: (val: string) => string;
export declare const uCharHexNumber: (val: string) => string;
export declare const universalMeasureValue: (val: UniversalMeasure) => UniversalMeasure;
export declare const positiveUniversalMeasureValue: (val: PositiveUniversalMeasure) => PositiveUniversalMeasure;
export declare const hexColorValue: (val: string) => string;
export declare const signedTwipsMeasureValue: (val: UniversalMeasure | number) => UniversalMeasure | number;
export declare const hpsMeasureValue: (val: PositiveUniversalMeasure | number) => string | number;
export declare const signedHpsMeasureValue: (val: UniversalMeasure | number) => string | number;
export declare const twipsMeasureValue: (val: PositiveUniversalMeasure | number) => PositiveUniversalMeasure | number;
export declare const percentageValue: (val: Percentage) => Percentage;
export declare const measurementOrPercentValue: (val: number | Percentage | UniversalMeasure) => number | UniversalMeasure | Percentage;
export declare const eighthPointMeasureValue: (val: number) => number;
export declare const pointMeasureValue: (val: number) => number;
export declare const dateTimeValue: (val: Date) => string;
