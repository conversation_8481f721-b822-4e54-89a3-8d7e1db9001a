import { XmlComponent } from '../../xml-components';
import { UniversalMeasure } from '../../../util/values';
export declare class CharacterSpacing extends XmlComponent {
    constructor(value: number | UniversalMeasure);
}
export declare class Color extends XmlComponent {
    constructor(color: string);
}
export declare class Highlight extends XmlComponent {
    constructor(color: string);
}
export declare class HighlightComplexScript extends XmlComponent {
    constructor(color: string);
}
