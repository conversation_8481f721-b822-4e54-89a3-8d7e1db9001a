import { XmlComponent } from '../xml-components';
export interface ICompatibilityOptions {
    readonly version?: number;
    readonly useSingleBorderforContiguousCells?: boolean;
    readonly wordPerfectJustification?: boolean;
    readonly noTabStopForHangingIndent?: boolean;
    readonly noLeading?: boolean;
    readonly spaceForUnderline?: boolean;
    readonly noColumnBalance?: boolean;
    readonly balanceSingleByteDoubleByteWidth?: boolean;
    readonly noExtraLineSpacing?: boolean;
    readonly doNotLeaveBackslashAlone?: boolean;
    readonly underlineTrailingSpaces?: boolean;
    readonly doNotExpandShiftReturn?: boolean;
    readonly spacingInWholePoints?: boolean;
    readonly lineWrapLikeWord6?: boolean;
    readonly printBodyTextBeforeHeader?: boolean;
    readonly printColorsBlack?: boolean;
    readonly spaceWidth?: boolean;
    readonly showBreaksInFrames?: boolean;
    readonly subFontBySize?: boolean;
    readonly suppressBottomSpacing?: boolean;
    readonly suppressTopSpacing?: boolean;
    readonly suppressSpacingAtTopOfPage?: boolean;
    readonly suppressTopSpacingWP?: boolean;
    readonly suppressSpBfAfterPgBrk?: boolean;
    readonly swapBordersFacingPages?: boolean;
    readonly convertMailMergeEsc?: boolean;
    readonly truncateFontHeightsLikeWP6?: boolean;
    readonly macWordSmallCaps?: boolean;
    readonly usePrinterMetrics?: boolean;
    readonly doNotSuppressParagraphBorders?: boolean;
    readonly wrapTrailSpaces?: boolean;
    readonly footnoteLayoutLikeWW8?: boolean;
    readonly shapeLayoutLikeWW8?: boolean;
    readonly alignTablesRowByRow?: boolean;
    readonly forgetLastTabAlignment?: boolean;
    readonly adjustLineHeightInTable?: boolean;
    readonly autoSpaceLikeWord95?: boolean;
    readonly noSpaceRaiseLower?: boolean;
    readonly doNotUseHTMLParagraphAutoSpacing?: boolean;
    readonly layoutRawTableWidth?: boolean;
    readonly layoutTableRowsApart?: boolean;
    readonly useWord97LineBreakRules?: boolean;
    readonly doNotBreakWrappedTables?: boolean;
    readonly doNotSnapToGridInCell?: boolean;
    readonly selectFieldWithFirstOrLastCharacter?: boolean;
    readonly applyBreakingRules?: boolean;
    readonly doNotWrapTextWithPunctuation?: boolean;
    readonly doNotUseEastAsianBreakRules?: boolean;
    readonly useWord2002TableStyleRules?: boolean;
    readonly growAutofit?: boolean;
    readonly useFELayout?: boolean;
    readonly useNormalStyleForList?: boolean;
    readonly doNotUseIndentAsNumberingTabStop?: boolean;
    readonly useAlternateEastAsianLineBreakRules?: boolean;
    readonly allowSpaceOfSameStyleInTable?: boolean;
    readonly doNotSuppressIndentation?: boolean;
    readonly doNotAutofitConstrainedTables?: boolean;
    readonly autofitToFirstFixedWidthCell?: boolean;
    readonly underlineTabInNumberingList?: boolean;
    readonly displayHangulFixedWidth?: boolean;
    readonly splitPgBreakAndParaMark?: boolean;
    readonly doNotVerticallyAlignCellWithSp?: boolean;
    readonly doNotBreakConstrainedForcedTable?: boolean;
    readonly ignoreVerticalAlignmentInTextboxes?: boolean;
    readonly useAnsiKerningPairs?: boolean;
    readonly cachedColumnBalance?: boolean;
}
export declare class Compatibility extends XmlComponent {
    constructor(options: ICompatibilityOptions);
}
