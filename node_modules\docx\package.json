{"name": "docx", "version": "8.5.0", "description": "Easily generate .docx files with JS/TS with a nice declarative API. Works for Node and on the Browser.", "type": "module", "main": "build/index.umd.js", "module": "./build/index.mjs", "types": "./build/index.d.ts", "exports": {".": {"require": "./build/index.cjs", "types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.mjs"}}, "files": ["build"], "scripts": {"build": "tsc && vite build", "test": "vitest --ui --coverage", "test:ci": "vitest run --coverage", "prepublishOnly": "npm run build --omit=dev", "lint": "eslint --ext .ts src", "predemo": "npm run build", "demo": "tsx ./demo/index.ts", "typedoc": "typedoc src/index.ts --tsconfig tsconfig.typedoc.json", "prettier": "prettier -l \"{src,scripts,demo}/**/*.{ts,html}\"", "prettier:fix": "npm run prettier -- --write", "cspell": "cspell \"{src,demo,docs,scripts}/**/*.{ts,scss,html,md}\" && cspell \"./*.*\"", "serve.docs": "cd docs && docsify serve", "extract": "tsx scripts/extract-document.ts", "run-ts": "tsx"}, "pre-commit": ["prettier", "lint"], "repository": {"type": "git", "url": "git+https://github.com/dolanmiu/docx.git"}, "keywords": ["docx", "office", "word", "generate", "creator", "create", "document", "doc", "officegen", "clippy"], "dependencies": {"@types/node": "^20.3.1", "jszip": "^3.10.1", "nanoid": "^5.0.4", "xml": "^1.0.1", "xml-js": "^1.6.8"}, "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/dolanmiu/docx/issues"}, "homepage": "https://docx.js.org", "devDependencies": {"@types/inquirer": "^9.0.3", "@types/prompt": "^1.1.1", "@types/unzipper": "^0.10.4", "@types/xml": "^1.0.8", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "@vitest/coverage-v8": "^1.1.0", "@vitest/ui": "^1.1.0", "cspell": "^8.2.3", "docsify-cli": "^4.3.0", "eslint": "^8.23.0", "eslint-plugin-functional": "^6.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsdoc": "^46.2.6", "eslint-plugin-no-null": "^1.0.2", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-unicorn": "^50.0.1", "execa": "^8.0.1", "glob": "^10.2.7", "inquirer": "^9.2.7", "jsdom": "^23.0.1", "pre-commit": "^1.2.2", "prettier": "^3.1.1", "tsconfig-paths": "^4.0.0", "tsx": "^4.7.0", "typedoc": "^0.25.4", "typescript": "5.3.3", "unzipper": "^0.10.11", "vite": "^5.0.10", "vite-plugin-dts": "^3.3.1", "vite-plugin-node-polyfills": "^0.19.0", "vite-tsconfig-paths": "^4.2.0", "vitest": "^1.1.0"}, "engines": {"node": ">=10"}}